import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { RAGType } from '@aos-enum/rag-type.enum';
import { TestType } from '@aos-enum/test-type.enum';
import { IBDSubtype } from '@aos-enum/ibd-subtype.enum';
import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import * as dayjs from 'dayjs';
import * as xl from 'excel4node';
import { promises as fsPromises } from 'fs';
import { Model } from 'mongoose';
import { filePathReport, seeder } from './../../app.config';
import { UserSchema } from './../../database/schema/user.schema';
import { TestResultStatus } from './../../shared/enums/test-result-status.enum';
import { UserRole } from './../../shared/enums/user-role.enum';
import { ResultInterface, TestResultDetailInterface, TestResultInterface } from './interface/time-up-and-go.interface';
import { SymptomsTestSubtype } from '@aos-enum/flag-threshold.enum';
const util = require('util');
const fs = require('fs');

const fontSizeDefault = 10;

@Injectable()
export class ReportService {
  constructor(
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<typeof UserSchema>,
    @Inject(getCollectionToken(AosCollections.TestResult))
    private readonly testResultModel: Model<any>,
  ) {}

  public async downloadReport(query) {
    const filePath = filePathReport + '/Report.xlsx';
    const testType = query.type ? parseInt(query.type) : null;
    await fsPromises.mkdir(filePathReport, { recursive: true });
    let result;

    if (query.patientId) {
      const user = await this.userModel.findById(query.patientId);

      if (!user) {
        throw new NotFoundException('User not found');
      }

      result = await this.getReports([user]);
    } else {
      const users = await this.userModel
        .find({
          role: UserRole.PATIENT,
        })
        .sort({ username: 1 });

      result = await this.getReports(users);
    }

    await this.exportDataToExcel(result, filePath, testType);

    return filePath;
  }

  public async downloadCSVReport(testType: TestType, query) {
    try {
      // Use UTC date for consistent file naming across timezones
      const currentDate = dayjs().utc().format('YYYY-MM-DD');
      const testTypeNames = {
        [TestType.SYMPTOMS]: 'Symptoms',
        [TestType.MENTAL_WELLBEING]: 'Mental_Wellbeing',
        [TestType.GENERAL_WELLBEING]: 'General_Wellbeing',
      };

      // File naming convention: Test1_Symptoms_2025-03-31.csv
      const fileName = `Test${testType}_${testTypeNames[testType]}_${currentDate}.csv`;
      const filePath = filePathReport + '/' + fileName;

      await fsPromises.mkdir(filePathReport, { recursive: true });

      let users;
      if (query.patientId) {
        const user = await this.userModel.findById(query.patientId);
        if (!user) {
          throw new NotFoundException('User not found');
        }
        users = [user];
      } else {
        users = await this.userModel
          .find({
            role: UserRole.PATIENT,
            isArchived: { $ne: true }, // Exclude archived patients
          })
          .sort({ username: 1 });
      }

      if (!users || users.length === 0) {
        throw new NotFoundException('No patients found for CSV generation');
      }

      const csvData = await this.generateCSVData(testType, users);

      if (!csvData || csvData.length === 0) {
        throw new NotFoundException(`No test results found for test type ${testType}`);
      }

      await this.exportDataToCSV(csvData, filePath, testType);

      return fileName;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to generate CSV report: ${error.message}`);
    }
  }

  public async downloadIndividualPatientCSV(patientId: string): Promise<string> {
    try {
      // Validate patient exists
      const patient = await this.userModel.findById(patientId);
      if (!patient) {
        throw new NotFoundException('Patient not found');
      }

      // Use UTC date for consistent file naming across timezones
      const currentDate = dayjs().utc().format('YYYY-MM-DD');
      const fileName = `${(patient as any).username}_${currentDate}.csv`;
      const filePath = filePathReport + '/' + fileName;

      await fsPromises.mkdir(filePathReport, { recursive: true });

      // Retrieve all test results for the patient
      const testResults = await this.testResultModel.aggregate([
        {
          $match: {
            patient: patient._id,
            status: { $ne: TestResultStatus.NEW },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'acknowledgedBy',
            foreignField: '_id',
            as: 'acknowledgedBy',
          },
        },
        { $sort: { type: 1, completedDate: -1 } },
      ]);

      // Check if there's any test data available
      const symptomsResults = testResults.filter((tr) => tr.type === TestType.SYMPTOMS);
      const mentalWellbeingResults = testResults.filter((tr) => tr.type === TestType.MENTAL_WELLBEING);
      const generalWellbeingResults = testResults.filter((tr) => tr.type === TestType.GENERAL_WELLBEING);

      // If no test results exist at all, throw error as specified in requirements
      if (testResults.length === 0) {
        throw new Error('There is currently no data available for this report.');
      }

      // Generate the four sheets
      const patientInfoSheet = this.generatePatientInfoSheet(patient);
      const symptomsSheet = this.generateIndividualPatientSymptomsSheet(patient, symptomsResults);
      const mentalWellbeingSheet = this.generateIndividualPatientMentalWellbeingSheet(patient, mentalWellbeingResults);
      const generalWellbeingSheet = this.generateIndividualPatientGeneralWellbeingSheet(
        patient,
        generalWellbeingResults,
      );

      // Combine all sheets into a single CSV
      const combinedCSV = this.combineMultiSheetCSV([
        patientInfoSheet,
        symptomsSheet,
        mentalWellbeingSheet,
        generalWellbeingSheet,
      ]);

      // Write to file
      await fsPromises.writeFile(filePath, combinedCSV, 'utf8');

      return fileName;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to generate individual patient CSV: ${error.message}`);
    }
  }

  private async generateCSVData(testType: TestType, users) {
    const userIds = users.map((user) => user._id);

    // Use aggregation for better performance with large datasets
    const testResults = await this.testResultModel.aggregate([
      {
        $match: {
          patient: { $in: userIds },
          type: testType,
          status: { $ne: TestResultStatus.NEW },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'patient',
          foreignField: '_id',
          as: 'patient',
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'acknowledgedBy',
          foreignField: '_id',
          as: 'acknowledgedBy',
        },
      },
      { $unwind: '$patient' },
      {
        $unwind: {
          path: '$acknowledgedBy',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          'patient.isArchived': { $ne: true },
        },
      },
      { $sort: { 'patient.username': 1, completedDate: -1 } },
    ]);

    // Group test results by patient
    const groupedData = this.groupTestResultsByPatient(testResults, testType);

    return groupedData;
  }

  private groupTestResultsByPatient(testResults: any[], testType: TestType) {
    const csvData = [];
    const patientGroups = new Map();

    // Group test results by patient ID
    for (const testResult of testResults) {
      const patientId = testResult.patient._id.toString();
      if (!patientGroups.has(patientId)) {
        patientGroups.set(patientId, {
          patient: testResult.patient,
          results: [],
        });
      }
      patientGroups.get(patientId).results.push(testResult);
    }

    // Generate CSV rows with patient grouping
    for (const [, group] of patientGroups) {
      // Add patient header row
      csvData.push(this.createPatientHeaderRow(group.patient));

      // Add column headers for this patient's test results
      // Pass the first test result as a sample to determine subtype for symptoms tests
      const sampleTestResult = group.results.length > 0 ? group.results[0] : null;
      csvData.push(this.createTestResultHeaderRow(testType, sampleTestResult));

      // Add test result rows for this patient (without patient info columns)
      for (const testResult of group.results) {
        if (testType === TestType.SYMPTOMS) {
          csvData.push(this.formatSymptomsCSVRowGrouped(testResult));
        } else if (testType === TestType.MENTAL_WELLBEING) {
          csvData.push(this.formatMentalWellbeingCSVRowGrouped(testResult));
        } else if (testType === TestType.GENERAL_WELLBEING) {
          csvData.push(this.formatGeneralWellbeingCSVRowGrouped(testResult));
        }
      }

      // Add empty row between patients for better readability
      csvData.push({});
    }

    return csvData;
  }

  private async exportDataToCSV(data: any[], filePath: string, testType: TestType) {
    try {
      // For grouped format, we need to manually construct the CSV
      const csvLines = [];

      for (const row of data) {
        if (row.patientHeader) {
          // Patient header row
          csvLines.push(`${row.patientHeader},${row.nhsHeader}`);
        } else if (this.isHeaderRow(row, testType)) {
          // Column headers row - use the actual header values from the row instead of regenerating
          const headerValues = this.getActualHeaderValues(row);
          csvLines.push(headerValues.join(','));
        } else if (Object.keys(row).length === 0) {
          // Empty row between patients
          csvLines.push('');
        } else {
          // Test result data row
          const dataValues = this.getDataValues(row, testType);
          csvLines.push(dataValues.join(','));
        }
      }

      // Write the CSV content to file
      const csvContent = csvLines.join('\n');
      await fsPromises.writeFile(filePath, csvContent, 'utf8');
    } catch (error) {
      throw new Error(`Failed to write CSV file: ${error.message}`);
    }
  }

  private isHeaderRow(row: any, testType: TestType): boolean {
    // For header detection, we use generic headers since we don't have subtype context
    const headers = this.getCSVHeaders(testType);
    // Check if this row contains header titles rather than data
    return headers.some(
      (header) => header.id !== 'username' && header.id !== 'nhsNumber' && row[header.id] === header.title,
    );
  }

  private getActualHeaderValues(headerRow: any): string[] {
    const values = [];

    // Extract header values from the actual header row, excluding patient info columns
    Object.keys(headerRow).forEach((key) => {
      if (key !== 'username' && key !== 'nhsNumber') {
        values.push(this.escapeCsvValue(headerRow[key]));
      }
    });

    return values;
  }

  private getHeaderValues(testType: TestType): string[] {
    // For header values, we use generic headers since we don't have subtype context
    const headers = this.getCSVHeaders(testType);
    const values = [];

    headers.forEach((header) => {
      if (header.id !== 'username' && header.id !== 'nhsNumber') {
        values.push(this.escapeCsvValue(header.title));
      }
    });

    return values;
  }

  private getDataValues(row: any, testType: TestType): string[] {
    // For data values, we use generic headers since we don't have subtype context
    const headers = this.getCSVHeaders(testType);
    const values = [];

    headers.forEach((header) => {
      if (header.id !== 'username' && header.id !== 'nhsNumber') {
        const value = row[header.id] || '';
        values.push(this.escapeCsvValue(value.toString()));
      }
    });

    return values;
  }

  private escapeCsvValue(value: string): string {
    // Escape CSV values that contain commas, quotes, or newlines
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    return value;
  }

  private getCSVHeaders(testType: TestType, ibdSubtype?: string) {
    if (testType === TestType.SYMPTOMS) {
      const baseHeaders = [
        { id: 'username', title: 'Username' },
        { id: 'nhsNumber', title: 'NHS number' },
        { id: 'dateTime', title: 'Date & Time' },
        { id: 'subtype', title: 'Subtype' },
        { id: 'redFlags', title: 'Red Flags' },
        { id: 'amberFlags', title: 'Amber Flags' },
        { id: 'greenFlags', title: 'Green flags' },
        { id: 'incomplete', title: 'Incomplete' },
        { id: 'flag', title: 'Flag' },
        { id: 'finalScore', title: 'Final Score' },
        { id: 'notes', title: 'Notes' },
      ];

      // Add dynamic question columns with descriptive headers
      const questionTextMappings = this.getSymptomsQuestionTextMappings();
      const questionHeaders = [];

      if (ibdSubtype === SymptomsTestSubtype.HBI) {
        // HBI questions (6 questions)
        for (let i = 1; i <= 6; i++) {
          const questionCode = `HBI_${i}`;
          const questionText = questionTextMappings[questionCode] || `Question ${i}`;
          questionHeaders.push({ id: `q${i}`, title: `Q${i} - ${questionText}` });
        }
      } else if (ibdSubtype === SymptomsTestSubtype.SCCAI) {
        // SCCAI questions (8 questions)
        for (let i = 1; i <= 8; i++) {
          const questionCode = `SCCAI_${i}`;
          const questionText = questionTextMappings[questionCode] || `Question ${i}`;
          questionHeaders.push({ id: `q${i}`, title: `Q${i} - ${questionText}` });
        }
      } else {
        // Fallback for unknown subtype - use generic headers with max 8 questions
        for (let i = 1; i <= 8; i++) {
          questionHeaders.push({ id: `q${i}`, title: `Q${i}` });
        }
      }

      return [...baseHeaders, ...questionHeaders];
    } else if (testType === TestType.MENTAL_WELLBEING) {
      const baseHeaders = [
        { id: 'username', title: 'Username' },
        { id: 'nhsNumber', title: 'NHS number' },
        { id: 'dateTime', title: 'Date & Time' },
        { id: 'incomplete', title: 'Incomplete' },
        { id: 'flag', title: 'Flag' },
        { id: 'hadsAnxietyScore', title: 'HADS Anxiety Score' },
        { id: 'hadsDepressionScore', title: 'HADS Depression Score' },
        { id: 'notes', title: 'Notes' },
      ];

      // Add dynamic question columns with descriptive headers
      const questionTextMappings = this.getMentalWellbeingQuestionTextMappings();
      const questionHeaders = [];

      for (let i = 1; i <= 14; i++) {
        const questionCode = `HADS_${i}`;
        const questionText = questionTextMappings[questionCode] || `Question ${i}`;
        questionHeaders.push({ id: `q${i}`, title: `Q${i} - ${questionText}` });
      }

      return [...baseHeaders, ...questionHeaders];
    } else if (testType === TestType.GENERAL_WELLBEING) {
      const baseHeaders = [
        { id: 'username', title: 'Username' },
        { id: 'nhsNumber', title: 'NHS number' },
        { id: 'dateTime', title: 'Date & Time' },
        { id: 'incomplete', title: 'Incomplete' },
        { id: 'flag', title: 'Flag' },
        { id: 'overdueFlag', title: 'Overdue Flag' },
        { id: 'sibdqScore', title: 'SIBDQ Score' },
        { id: 'eqScore', title: 'EQ Score' },
        { id: 'notes', title: 'Notes' },
      ];

      // Add dynamic question columns with descriptive headers
      const questionTextMappings = this.getGeneralWellbeingQuestionTextMappings();
      const questionHeaders = [];

      for (let i = 1; i <= 22; i++) {
        const questionCode = `GW_${i}`;
        const questionText = questionTextMappings[questionCode] || `Question ${i}`;
        questionHeaders.push({ id: `q${i}`, title: `Q${i} - ${questionText}` });
      }

      // Additional blank columns for manual clinician input
      const clinicianHeaders = [
        { id: 'week12ScanOutcomes', title: '12 week scan outcomes' },
        { id: 'week20ScanOutcomes', title: '20 week scan outcomes' },
        { id: 'vteDuringPregnancy', title: 'VTE during pregnancy' },
        { id: 'vteProphylaxisGiven', title: 'VTE prophylaxis given' },
        { id: 'birthOutcomes', title: 'Birth outcomes' },
        { id: 'birthWeight', title: 'Birth weight' },
        { id: 'birthOutcomes2', title: 'Birth outcomes' }, // Note: appears twice in requirements
        { id: 'modeOfDelivery', title: 'Mode of delivery' },
        { id: 'complications', title: 'Complications' },
        { id: 'gestationalSize', title: 'Gestational size' },
        { id: 'gestationalAge', title: 'Gestational age' },
        { id: 'ibdOutcomes', title: 'IBD outcomes' },
        { id: 'steroidsDuringPregnancy', title: 'Steroids during pregnancy' },
        { id: 'startedBiologicDuringPregnancy', title: 'Started biologic during pregnancy' },
      ];

      return [...baseHeaders, ...questionHeaders, ...clinicianHeaders];
    }
    return [];
  }

  // New methods for grouped CSV format
  private createPatientHeaderRow(patient: any) {
    return {
      patientHeader: `${patient.username || ''}`,
      nhsHeader: `NHS: ${patient.nhsNumber || ''}`,
    };
  }

  private createTestResultHeaderRow(testType: TestType, sampleTestResult?: any) {
    // For symptoms tests, try to determine the subtype from the sample test result
    let ibdSubtype = undefined;
    if (testType === TestType.SYMPTOMS && sampleTestResult?.patient) {
      const user = sampleTestResult.patient;
      ibdSubtype = user.ibdSubtype === IBDSubtype.CROHNS_DISEASE
        ? SymptomsTestSubtype.HBI
        : user.ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS
          ? SymptomsTestSubtype.SCCAI
          : undefined;
    }

    const headers = this.getCSVHeaders(testType, ibdSubtype);
    const headerRow = {};

    // Skip username and nhsNumber columns for grouped format
    headers.forEach((header) => {
      if (header.id !== 'username' && header.id !== 'nhsNumber') {
        headerRow[header.id] = header.title;
      }
    });

    return headerRow;
  }

  private formatSymptomsCSVRowGrouped(testResult: any) {
    const user = testResult.patient;
    const ibdSubtype =
      user.ibdSubtype === IBDSubtype.CROHNS_DISEASE
        ? SymptomsTestSubtype.HBI
        : user.ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS
          ? SymptomsTestSubtype.SCCAI
          : '';

    const isIncomplete = testResult.status !== TestResultStatus.COMPLETED;
    const hasFlag = testResult.redFlag || testResult.reds > 0 || testResult.ambers > 0;

    const row = {
      dateTime: testResult.completedDate ? dayjs(testResult.completedDate).utc().format('YYYY-MM-DD HH:mm:ss') : '',
      subtype: ibdSubtype,
      redFlags: testResult.reds || 0,
      amberFlags: testResult.ambers || 0,
      greenFlags: testResult.greens || 0,
      incomplete: isIncomplete ? 'Yes' : 'No',
      flag: hasFlag ? 'Yes' : 'No',
      finalScore: testResult.totalScores || '',
      notes: testResult?.notes || '',
      q1: '',
      q2: '',
      q3: '',
      q4: '',
      q5: '',
      q6: '',
      q7: '',
      q8: '',
    };

    // Add question answers based on report data with human-readable text conversion
    if (testResult.report && Array.isArray(testResult.report)) {
      const processedQuestions = this.processSymptomsQuestionsForCSV(testResult.report, ibdSubtype);

      // Map processed questions to Q1, Q2, etc. format
      Object.keys(processedQuestions).forEach((questionKey) => {
        if (row.hasOwnProperty(questionKey)) {
          row[questionKey] = processedQuestions[questionKey];
        }
      });
    }

    return row;
  }

  /**
   * Process symptoms questions for CSV export with human-readable text conversion
   */
  private processSymptomsQuestionsForCSV(reportItems: any[], ibdSubtype: string): Record<string, string> {
    const processedQuestions: Record<string, string> = {};

    if (ibdSubtype === SymptomsTestSubtype.HBI) {
      return this.processHBIQuestionsForCSV(reportItems);
    } else if (ibdSubtype === SymptomsTestSubtype.SCCAI) {
      return this.processSCCAIQuestionsForCSV(reportItems);
    }

    return processedQuestions;
  }

  /**
   * Process HBI questions for CSV export
   */
  private processHBIQuestionsForCSV(reportItems: any[]): Record<string, string> {
    const questions: Record<string, string> = {
      q1: '',
      q2: '',
      q3: '',
      q4: '',
      q5: '',
      q6: '',
    };

    // Group report items by question code
    const questionGroups = this.groupReportItemsByQuestion(reportItems);

    // Process each HBI question
    questions.q1 = this.convertHBIQ1Answer(questionGroups['HBI_1']);
    questions.q2 = this.convertHBIQ2Answer(questionGroups['HBI_2']);
    questions.q3 = this.convertHBIQ3Answer(questionGroups['HBI_3']);
    questions.q4 = this.convertHBIQ4Answer(questionGroups['HBI_4']);
    questions.q5 = this.convertHBIQ5Answer(questionGroups['HBI_5'], questionGroups['HBI_6']);
    questions.q6 = this.convertHBIQ6Answer(questionGroups['HBI_6']);

    return questions;
  }

  /**
   * Process SCCAI questions for CSV export
   */
  private processSCCAIQuestionsForCSV(reportItems: any[]): Record<string, string> {
    const questions: Record<string, string> = {
      q1: '',
      q2: '',
      q3: '',
      q4: '',
      q5: '',
      q6: '',
      q7: '',
      q8: '',
    };

    // Group report items by question code
    const questionGroups = this.groupReportItemsByQuestion(reportItems);

    // Process each SCCAI question
    questions.q1 = this.convertSCCAIQ1Answer(questionGroups['SCCAI_1']);
    questions.q2 = this.convertSCCAIQ2Answer(questionGroups['SCCAI_2']);
    questions.q3 = this.convertSCCAIQ3Answer(questionGroups['SCCAI_3']);
    questions.q4 = this.convertSCCAIQ4Answer(questionGroups['SCCAI_4']);
    questions.q5 = this.convertSCCAIQ5Answer(questionGroups['SCCAI_5']);
    questions.q6 = this.convertSCCAIQ6Answer(questionGroups['SCCAI_6']);
    questions.q7 = this.convertSCCAIQ7Answer(questionGroups['SCCAI_7'], questionGroups['SCCAI_8']);
    questions.q8 = this.convertSCCAIQ8Answer(questionGroups['SCCAI_8']);

    return questions;
  }

  /**
   * Group report items by their question code prefix
   */
  private groupReportItemsByQuestion(reportItems: any[]): Record<string, any[]> {
    const groups: Record<string, any[]> = {};

    reportItems.forEach((item) => {
      if (item.code) {
        // Extract base question code (e.g., 'HBI_1' from 'HBI_1_A')
        const baseCode = item.code.includes('_') ? item.code.split('_').slice(0, 2).join('_') : item.code;

        if (!groups[baseCode]) {
          groups[baseCode] = [];
        }
        groups[baseCode].push(item);
      }
    });

    return groups;
  }

  /**
   * Get question text mappings for Symptoms tests from test data
   */
  private getSymptomsQuestionTextMappings(): Record<string, string> {
    try {
      const rawdata = fs.readFileSync(seeder.testDataPath);
      const records = JSON.parse(rawdata) as any[];

      const mappings: Record<string, string> = {};

      // Find HBI questions
      const hbiTest = records.find((record) => record.title === 'Harvey-Bradshaw Index (HBI)');
      if (hbiTest && hbiTest.questions) {
        hbiTest.questions.forEach((question: any) => {
          if (question.code && question.text) {
            mappings[question.code] = question.text;
          }
          // Handle nested questions (like HBI_6)
          if (question.questions) {
            question.questions.forEach((nestedQuestion: any) => {
              if (nestedQuestion.code && nestedQuestion.text) {
                mappings[nestedQuestion.code] = nestedQuestion.text;
              }
            });
          }
        });
      }

      // Find SCCAI questions
      const sccaiTest = records.find((record) => record.title === 'Simple Clinical Colitis Activity Index (SCCAI)');
      if (sccaiTest && sccaiTest.questions) {
        sccaiTest.questions.forEach((question: any) => {
          if (question.code && question.text) {
            mappings[question.code] = question.text;
          }
          // Handle nested questions (like SCCAI_8)
          if (question.questions) {
            question.questions.forEach((nestedQuestion: any) => {
              if (nestedQuestion.code && nestedQuestion.text) {
                mappings[nestedQuestion.code] = nestedQuestion.text;
              }
            });
          }
        });
      }

      return mappings;
    } catch (error) {
      console.error('Error loading symptoms question text mappings:', error);
      return {};
    }
  }

  /**
   * Get question text mappings for Mental Wellbeing tests from test data
   */
  private getMentalWellbeingQuestionTextMappings(): Record<string, string> {
    try {
      const rawdata = fs.readFileSync(seeder.testDataPath);
      const records = JSON.parse(rawdata) as any[];

      const mappings: Record<string, string> = {};

      // Find Mental Wellbeing questions
      const mentalWellbeingTest = records.find((record) => record.title === 'Mental Wellbeing');
      if (mentalWellbeingTest && mentalWellbeingTest.questions) {
        mentalWellbeingTest.questions.forEach((question: any) => {
          if (question.code && question.text) {
            mappings[question.code] = question.text;
          }
        });
      }

      return mappings;
    } catch (error) {
      console.error('Error loading mental wellbeing question text mappings:', error);
      return {};
    }
  }

  /**
   * Get question text mappings for General Wellbeing tests from test data
   */
  private getGeneralWellbeingQuestionTextMappings(): Record<string, string> {
    try {
      const rawdata = fs.readFileSync(seeder.testDataPath);
      const records = JSON.parse(rawdata) as any[];

      const mappings: Record<string, string> = {};

      // Find General Wellbeing questions
      const generalWellbeingTest = records.find((record) => record.title === 'General Wellbeing');
      if (generalWellbeingTest && generalWellbeingTest.questions) {
        generalWellbeingTest.questions.forEach((question: any) => {
          if (question.code && question.text) {
            mappings[question.code] = question.text;
          }
        });
      }

      return mappings;
    } catch (error) {
      console.error('Error loading general wellbeing question text mappings:', error);
      return {};
    }
  }

  // HBI Question Conversion Methods
  private convertHBIQ1Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      A: 'Very well',
      B: 'Slightly below par',
      C: 'Poor',
      D: 'Very poor',
      E: 'Terrible',
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertHBIQ2Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      A: 'None',
      B: 'Mild',
      C: 'Moderate',
      D: 'Severe',
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertHBIQ3Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      A: '0',
      B: '1',
      C: '2',
      D: '3',
      E: '4',
      F: '5+',
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertHBIQ4Answer(items: any[]): string {
    if (!items || items.length === 0) return '';

    const codeToText = {
      A: 'Arthralgia (joint pain)',
      B: 'Uveitis (eye inflammation)',
      C: 'Erythema nodosum (painful rash on legs)',
      D: 'Aphthous ulcers (type of mouth ulcer)',
      E: 'Pyoderma gangrenosum (type of necrotic skin ulcer)',
      F: 'Anal fissures (anal tear)',
      G: 'New fistula (channel between bowel and skin)',
      H: 'Abscess (pus-filled)',
    };

    // Multiple answers possible for checkbox type - use pipe separator
    const answers = items.map((item) => codeToText[item.answer] || item.answer).filter(Boolean);
    return answers.join(' | ');
  }

  private convertHBIQ5Answer(q5Items: any[], q6Items: any[]): string {
    if (!q5Items || q5Items.length === 0) return '';
    const item = q5Items[0];

    let result = '';
    if (item.answer === 'Yes' || item.answer === 'No') {
      result = item.answer;

      // Add date if available
      if (item.testDate) {
        const formattedDate = dayjs(item.testDate).format('YYYY-MM-DD');
        result += ` (${formattedDate})`;
      }
    } else {
      result = item.answer || '';
    }

    return result;
  }

  private convertHBIQ6Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];

    // For numeric input, show the actual numeric value
    if (item.numericValue !== undefined && item.numericValue !== null) {
      return item.numericValue.toString();
    }

    return item.answer || item.score?.toString() || '';
  }

  // SCCAI Question Conversion Methods
  private convertSCCAIQ1Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      A: '0-3',
      B: '4-6',
      C: '7-9',
      D: '9+',
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertSCCAIQ2Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      A: '0',
      B: '1-3',
      C: '4+',
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertSCCAIQ3Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      A: 'Not urgent',
      B: 'Hurried',
      C: 'Immediately urgent',
      D: 'No warning, incontinence',
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertSCCAIQ4Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      A: 'None',
      B: 'Trace',
      C: 'Occasionally frank',
      D: 'Usually frank',
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertSCCAIQ5Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];
    const codeToText = {
      A: 'Very well',
      B: 'Slightly below par',
      C: 'Poor',
      D: 'Very poor',
      E: 'Terrible',
    };
    return codeToText[item.answer] || item.answer || '';
  }

  private convertSCCAIQ6Answer(items: any[]): string {
    if (!items || items.length === 0) return '';

    const codeToText = {
      A: 'Arthralgia (joint pain)',
      B: 'Uveitis (eye inflammation)',
      C: 'Erythema nodosum (painful rash on legs)',
      D: 'Aphthous ulcers (type of mouth ulcer)',
      E: 'Pyoderma gangrenosum (type of necrotic skin ulcer)',
      F: 'Anal fissures (anal tear)',
      G: 'New fistula (channel between bowel and skin)',
      H: 'Abscess (pus-filled)',
    };

    // Multiple answers possible for checkbox type - use pipe separator
    const answers = items.map((item) => codeToText[item.answer] || item.answer).filter(Boolean);
    return answers.join(' | ');
  }

  private convertSCCAIQ7Answer(q7Items: any[], q8Items: any[]): string {
    if (!q7Items || q7Items.length === 0) return '';
    const item = q7Items[0];

    let result = '';
    if (item.answer === 'Yes' || item.answer === 'No') {
      result = item.answer;

      // Add date if available
      if (item.testDate) {
        const formattedDate = dayjs(item.testDate).format('YYYY-MM-DD');
        result += ` (${formattedDate})`;
      }
    } else {
      result = item.answer || '';
    }

    return result;
  }

  private convertSCCAIQ8Answer(items: any[]): string {
    if (!items || items.length === 0) return '';
    const item = items[0];

    // For numeric input, show the actual numeric value
    if (item.numericValue !== undefined && item.numericValue !== null) {
      return item.numericValue.toString();
    }

    return item.answer || item.score?.toString() || '';
  }

  /**
   * Process Mental Wellbeing questions for CSV export with human-readable text conversion
   */
  private processMentalWellbeingQuestionsForCSV(reportItems: any[]): Record<string, string> {
    const questions: Record<string, string> = {};

    // Initialize all question slots (14 HADS questions)
    for (let i = 1; i <= 14; i++) {
      questions[`q${i}`] = '';
    }

    // Group report items by question code
    const questionGroups = this.groupReportItemsByQuestion(reportItems);

    // Process each mental wellbeing question
    // Based on the test data structure, we need to handle HADS_1 through HADS_14
    for (let i = 1; i <= 14; i++) {
      const questionCode = `HADS_${i}`;
      const questionKey = `q${i}`;

      if (questionGroups[questionCode]) {
        questions[questionKey] = this.convertMentalWellbeingAnswer(questionGroups[questionCode], i);
      }
    }

    return questions;
  }

  /**
   * Process General Wellbeing questions for CSV export with human-readable text conversion
   */
  private processGeneralWellbeingQuestionsForCSV(reportItems: any[]): Record<string, string> {
    const questions: Record<string, string> = {};

    // Initialize all question slots (22 General Wellbeing questions)
    for (let i = 1; i <= 22; i++) {
      questions[`q${i}`] = '';
    }

    // Group report items by question code
    const questionGroups = this.groupReportItemsByQuestion(reportItems);

    // Process each general wellbeing question
    // Based on the test data structure, we need to handle GW_1 through GW_22
    for (let i = 1; i <= 22; i++) {
      const questionCode = `GW_${i}`;
      const questionKey = `q${i}`;

      if (questionGroups[questionCode]) {
        questions[questionKey] = this.convertGeneralWellbeingAnswer(questionGroups[questionCode], i);
      }
    }

    return questions;
  }

  /**
   * Convert Mental Wellbeing (HADS) answers to human-readable text
   */
  private convertMentalWellbeingAnswer(items: any[], questionNumber: number): string {
    if (!items || items.length === 0) return '';
    const item = items[0];

    // Define answer mappings for each HADS question
    const answerMappings = {
      1: { // HADS_1: To what extent do you feel tense or 'wound up'?
        A: 'Most of the time',
        B: 'A lot of the time',
        C: 'From time to time, occasionally',
        D: 'Not at all',
      },
      2: { // HADS_2: To what extent do you feel slowed down?
        A: 'Nearly all the time',
        B: 'Very often',
        C: 'Sometimes',
        D: 'Not at all',
      },
      3: { // HADS_3: To what extent do you feel a frightened feeling like 'butterflies' in the stomach?
        A: 'Very often',
        B: 'Quite often',
        C: 'Occasionally',
        D: 'Not at all',
      },
      4: { // HADS_4: Do you still enjoy the things you used to enjoy?
        A: 'Definitely',
        B: 'Not quite so much',
        C: 'A little',
        D: 'Hardly at all',
      },
      5: { // HADS_5: Do you feel frightened as if something awful is about to happen?
        A: 'Definitely and quite badly',
        B: 'Yes, but not too badly',
        C: "A little, but doesn't worry me",
        D: 'Not at all',
      },
      6: { // HADS_6: Do you feel you have lost interest in your appearance?
        A: 'Definitely',
        B: "I don't take as much care as i should",
        C: 'I may not take quite as much care',
        D: 'I take just as much care as ever',
      },
      7: { // HADS_7: Do you feel you can laugh and see the funny side of things?
        A: 'As much as I always could',
        B: 'Definitely not so much now',
        C: 'Not quite so much now',
        D: 'Not at all',
      },
      8: { // HADS_8: To what extent do you feel restless as if you have to be on the move?
        A: 'Very much indeed',
        B: 'Quite alot',
        C: 'Not very much',
        D: 'Not at all',
      },
      9: { // HADS_9: Do worrying thoughts go through your mind?
        A: 'A great deal of the time',
        B: 'A lot of the time',
        C: 'From time to time, not too often',
        D: 'Only occasionally',
      },
      10: { // HADS_10: Do you look forward with enjoyment to things?
        A: 'Hardly at all',
        B: 'Definitely less than I used to',
        C: 'Rather less than I used to',
        D: 'As much as I ever did',
      },
      11: { // HADS_11: Do you get sudden feelings of panic?
        A: 'Very often indeed',
        B: 'Quite often',
        C: 'Not very often',
        D: 'Not at all',
      },
      12: { // HADS_12: Do you feel cheerful?
        A: 'Not at all',
        B: 'Not often',
        C: 'Sometimes',
        D: 'Most of the time',
      },
      13: { // HADS_13: Are you able to sit at ease and feel relaxed?
        A: 'Not at all',
        B: 'Not often',
        C: 'Usually',
        D: 'Most of the time',
      },
      14: { // HADS_14: Can you enjoy a good book, radio or TV program?
        A: 'Very seldom',
        B: 'Not often',
        C: 'Sometimes',
        D: 'Often',
      },
    };

    const mapping = answerMappings[questionNumber];
    if (mapping && mapping[item.answer]) {
      return mapping[item.answer];
    }

    // Fallback to original answer if no mapping found
    return item.answer || '';
  }

  /**
   * Convert General Wellbeing answers to human-readable text
   */
  private convertGeneralWellbeingAnswer(items: any[], questionNumber: number): string {
    if (!items || items.length === 0) return '';
    const item = items[0];

    // Special handling for text input questions
    if (questionNumber === 1 || questionNumber === 5) {
      // Q1 and Q5: Display the input text value (not codes) for "Other" answers
      if (item.answer === 'B' && item.textValue) {
        return `Yes - Other (${item.textValue})`;
      }
      if (item.answer === 'Yes' && item.textValue) {
        return `Yes (${item.textValue})`;
      }
    }

    // Special handling for numeric input question
    if (questionNumber === 12) {
      // Q12: Display the numeric value
      if (item.numericValue !== undefined && item.numericValue !== null) {
        return item.numericValue.toString();
      }
      return item.answer || item.score?.toString() || '';
    }

    // Define answer mappings for each General Wellbeing question
    const answerMappings = {
      1: { // GW_1: Have you received any medicine to prevent blood clots?
        A: 'Yes - Clexane/enoxaparin injection',
        B: 'Yes - Other (please describe below)',
        C: 'No',
      },
      2: { // GW_2: Have you planned your mode of delivery?
        A: 'Yes - C-section',
        B: 'Yes - Vaginal delivery',
        C: 'No',
      },
      3: { // GW_3: Have you contacted your local IBD helpline in the last 3 months?
        A: 'No',
        B: 'Yes, 1-3 times',
        C: 'Yes, 4-6 times',
        D: 'Yes, 7-9 times',
        E: 'Yes, 9+ times',
      },
      4: { // GW_4: Have you needed urgent care for your IBD or pregnancy in the last 3 months?
        A: 'No',
        B: 'Yes, 1-3 times',
        C: 'Yes, 4-6 times',
        D: 'Yes, 7-9 times',
        E: 'Yes, 9+ times',
      },
      5: { // GW_5: Have you changed medications for your IBD in the last 3 months?
        Yes: 'Yes',
        No: 'No',
      },
      6: { // GW_6: Have you started a new course of steroids in the last 3 months?
        Yes: 'Yes',
        No: 'No',
      },
      7: { // GW_7: Please tick one box that describes your MOTILITY today.
        A: 'I have no problems in walking about',
        B: 'I have slight problems in walking about',
        C: 'I have moderate problems in walking about',
        D: 'I have severe problems in walking about',
        E: 'I am unable to walk about',
      },
      8: { // GW_8: Please tick one box that describes your SELF-CARE today.
        A: 'I have no problems with self-care',
        B: 'I have slight problems washing or dressing myself',
        C: 'I have moderate problems washing or dressing myself',
        D: 'I have severe problems washing or dressing myself',
        E: 'I am unable to wash or dress myself',
      },
      9: { // GW_9: Please tick one box that describes your PAIN / DISCOMFORT today.
        A: 'I have no pain or discomfort',
        B: 'I have slight pain or discomfort',
        C: 'I have moderate pain or discomfort',
        D: 'I have severe pain or discomfort',
        E: 'I have extreme pain or discomfort',
      },
      10: { // GW_10: Please tick one box that describes your ANXIETY / DEPRESSION today.
        A: 'I am not anxious or depressed',
        B: 'I am slightly anxious or depressed',
        C: 'I am moderately anxious or depressed',
        D: 'I am severely anxious or depressed',
        E: 'I am extremely anxious or depressed',
      },
      11: { // GW_11: Please tick one box that describes your USUAL ACTIVITIES today.
        A: 'I have no problems with performing my usual activities',
        B: 'I have slight problems with performing my usual activities',
        C: 'I have moderate problems with performing my usual activities',
        D: 'I have severe problems with performing my usual activities',
        E: 'I am unable to perform my usual activities',
      },
      // Question 12 is handled separately above (numeric input)
      13: { // GW_13: How frequent have your bowel motions been, during the last 2 weeks?
        A: 'More frequent than they have ever been',
        B: 'Much more frequent than usual',
        C: 'Somewhat more frequent than usual',
        D: 'About the same as usual',
        E: 'Somewhat less frequent than usual',
        F: 'Much less frequent than usual',
        G: 'Less frequent than they have ever been',
      },
      14: { // GW_14: How often, during the last 2 weeks, have you been troubled by abdominal cramps?
        A: 'All of the time',
        B: 'Most of the time',
        C: 'A good bit of the time',
        D: 'Some of the time',
        E: 'A little of the time',
        F: 'Hardly any of the time',
        G: 'None of the time',
      },
      15: { // GW_15: How often has the feeling of fatigue been a problem for you, during the last 2 weeks?
        A: 'All of the time',
        B: 'Most of the time',
        C: 'A good bit of the time',
        D: 'Some of the time',
        E: 'A little of the time',
        F: 'Hardly any of the time',
        G: 'None of the time',
      },
      16: { // GW_16: How much energy have you had during the last 2 weeks?
        A: 'No energy at all',
        B: 'A little energy',
        C: 'Some energy',
        D: 'Quite a bit of energy',
        E: 'A lot of energy',
      },
      17: { // GW_17: How often did you feel worried about the possibility of needing surgery?
        A: 'All of the time',
        B: 'Most of the time',
        C: 'A good bit of the time',
        D: 'Some of the time',
        E: 'A little of the time',
        F: 'Hardly any of the time',
        G: 'None of the time',
      },
      18: { // GW_18: How often have you been troubled by fear of not finding a toilet?
        A: 'All of the time',
        B: 'Most of the time',
        C: 'A good bit of the time',
        D: 'Some of the time',
        E: 'A little of the time',
        F: 'Hardly any of the time',
        G: 'None of the time',
      },
      19: { // GW_19: How often have you felt relaxed and free of tension?
        A: 'All of the time',
        B: 'Most of the time',
        C: 'A good bit of the time',
        D: 'Some of the time',
        E: 'A little of the time',
        F: 'Hardly any of the time',
        G: 'None of the time',
      },
      20: { // GW_20: How much of the time have you felt irritable in mood?
        A: 'All of the time',
        B: 'Most of the time',
        C: 'A good bit of the time',
        D: 'Some of the time',
        E: 'A little of the time',
        F: 'Hardly any of the time',
        G: 'None of the time',
      },
      21: { // GW_21: How much difficulty have you had doing leisure or sports activities?
        A: 'A great deal of difficulty',
        B: 'A lot of difficulty',
        C: 'Some difficulty',
        D: 'A little difficulty',
        E: 'No difficulty at all',
      },
      22: { // GW_22: To what extent has your IBD limited sexual activity?
        A: 'No sex at all',
        B: 'Major limitation',
        C: 'Moderate limitation',
        D: 'Some limitation',
        E: 'A little limitation',
        F: 'Hardly any limitation',
        G: 'No limitation whatsoever',
      },
    };

    const mapping = answerMappings[questionNumber];
    if (mapping && mapping[item.answer]) {
      return mapping[item.answer];
    }

    // Fallback to original answer if no mapping found
    return item.answer || item.textValue || item.score?.toString() || '';
  }

  private formatMentalWellbeingCSVRowGrouped(testResult: any) {
    const isIncomplete = testResult.status !== TestResultStatus.COMPLETED;
    const hasFlag = testResult.redFlag || testResult.anxietyScore >= 11 || testResult.depressionScore >= 11;

    const row = {
      dateTime: testResult.completedDate ? dayjs(testResult.completedDate).utc().format('YYYY-MM-DD HH:mm:ss') : '',
      incomplete: isIncomplete ? 'Yes' : 'No',
      flag: hasFlag ? 'Yes' : 'No',
      hadsAnxietyScore: testResult.anxietyScore || '',
      hadsDepressionScore: testResult.depressionScore || '',
      notes: testResult?.notes || '',
      q1: '',
      q2: '',
      q3: '',
      q4: '',
      q5: '',
      q6: '',
      q7: '',
      q8: '',
      q9: '',
      q10: '',
      q11: '',
      q12: '',
      q13: '',
      q14: '',
    };

    // Add question answers with human-readable text conversion for mental wellbeing
    if (testResult.report && Array.isArray(testResult.report)) {
      const processedQuestions = this.processMentalWellbeingQuestionsForCSV(testResult.report);

      // Map processed questions to Q1, Q2, etc. format
      Object.keys(processedQuestions).forEach((questionKey) => {
        if (row.hasOwnProperty(questionKey)) {
          row[questionKey] = processedQuestions[questionKey];
        }
      });
    }

    return row;
  }

  private formatGeneralWellbeingCSVRowGrouped(testResult: any) {
    const isIncomplete = testResult.status !== TestResultStatus.COMPLETED;
    const hasFlag = testResult.redFlag;
    const isOverdue = testResult.status === TestResultStatus.OVERDUE;

    const row = {
      dateTime: testResult.completedDate ? dayjs(testResult.completedDate).utc().format('YYYY-MM-DD HH:mm:ss') : '',
      incomplete: isIncomplete ? 'Yes' : 'No',
      flag: hasFlag ? 'Yes' : 'No',
      overdueFlag: isOverdue ? 'Yes' : 'No',
      sibdqScore: testResult.sibdqScore || '',
      eqScore: testResult.eqScore || '',
      notes: testResult?.notes || '',
      // Initialize all question slots (22 General Wellbeing questions)
      q1: '',
      q2: '',
      q3: '',
      q4: '',
      q5: '',
      q6: '',
      q7: '',
      q8: '',
      q9: '',
      q10: '',
      q11: '',
      q12: '',
      q13: '',
      q14: '',
      q15: '',
      q16: '',
      q17: '',
      q18: '',
      q19: '',
      q20: '',
      q21: '',
      q22: '',
      // Fix for Valid Defect #2: Add all missing manual input columns as blank fields
      week12ScanOutcomes: '',
      week20ScanOutcomes: '',
      vteDuringPregnancy: '',
      vteProphylaxisGiven: '',
      birthOutcomes: '',
      birthWeight: '',
      birthOutcomes2: '', // Note: appears twice in requirements
      modeOfDelivery: '',
      complications: '',
      gestationalSize: '',
      gestationalAge: '',
      ibdOutcomes: '',
      steroidsDuringPregnancy: '',
      startedBiologicDuringPregnancy: '',
    };

    // Add question answers with human-readable text conversion for general wellbeing
    if (testResult.report && Array.isArray(testResult.report)) {
      const processedQuestions = this.processGeneralWellbeingQuestionsForCSV(testResult.report);

      // Map processed questions to Q1, Q2, etc. format
      Object.keys(processedQuestions).forEach((questionKey) => {
        if (row.hasOwnProperty(questionKey)) {
          row[questionKey] = processedQuestions[questionKey];
        }
      });
    }

    return row;
  }

  private async getReports(users) {
    const result = {
      timeUpAndGo: [] as TestResultInterface[],
      symptoms: [] as TestResultInterface[],
      healthStatus: [] as TestResultInterface[],
    } as ResultInterface;

    for (const user of users) {
      const records = await this.fetchUserTestRecords(user._id);
      const categorizedResults = this.categorizeTestResults(records);
      this.addUserToResults(result, user, categorizedResults);
    }

    return result;
  }

  private async fetchUserTestRecords(userId: string) {
    return await this.testResultModel
      .find({
        patient: userId,
        status: { $ne: TestResultStatus.NEW },
      })
      .populate('acknowledgedBy')
      .sort({ createdAt: -1 });
  }

  private categorizeTestResults(records: any[]) {
    const timeUpAndGoResults = [] as TestResultDetailInterface[];
    const symptomsResults = [] as TestResultDetailInterface[];
    const healthStatusResults = [] as TestResultDetailInterface[];

    for (const record of records) {
      if (this.shouldSkipRecord(record)) {
        continue;
      }

      this.categorizeRecord(record, timeUpAndGoResults, symptomsResults, healthStatusResults);
    }

    return {
      timeUpAndGoResults,
      symptomsResults,
      healthStatusResults,
    };
  }

  private shouldSkipRecord(record: any): boolean {
    return record.status === TestResultStatus.OVERDUE && record.overDueCount != undefined && record.overDueCount !== 0;
  }

  private categorizeRecord(
    record: any,
    timeUpAndGoResults: TestResultDetailInterface[],
    symptomsResults: TestResultDetailInterface[],
    healthStatusResults: TestResultDetailInterface[],
  ) {
    if (record.type === TestType.SYMPTOMS) {
      symptomsResults.push(record as any);
    } else if (record.type === TestType.MENTAL_WELLBEING) {
      timeUpAndGoResults.push(record as any);
    } else {
      healthStatusResults.push(record as any);
    }
  }

  private addUserToResults(result: ResultInterface, user: any, categorizedResults: any) {
    const userInfo = this.createUserInfo(user);

    result.timeUpAndGo.push({
      ...userInfo,
      records: categorizedResults.timeUpAndGoResults,
    });

    result.symptoms.push({
      ...userInfo,
      records: categorizedResults.symptomsResults,
    });

    result.healthStatus.push({
      ...userInfo,
      records: categorizedResults.healthStatusResults,
    });
  }

  private createUserInfo(user: any) {
    return {
      username: user.username,
      fullName: user.firstName + ' ' + user.lastName,
      nhsNumber: user.nhsNumber,
    };
  }

  private async exportDataToExcel(data: ResultInterface, path, testType = null) {
    const wb = new xl.Workbook();
    wb.writeP = util.promisify(wb.write);
    const thinBorderStyle = wb.createStyle({
      border: {
        left: {
          style: 'thin',
          color: 'black',
        },
        right: {
          style: 'thin',
          color: 'black',
        },
        top: {
          style: 'thin',
          color: 'black',
        },
        bottom: {
          style: 'thin',
          color: 'black',
        },
        outline: false,
      },
    });

    if (testType === null) {
      this.createSymptomsSheet(data, wb, thinBorderStyle);
      this.createTimedUpAndGoSheet(data, wb, thinBorderStyle);
      this.createHealthStatusSheet(data, wb, thinBorderStyle);
    } else if (testType === TestType.SYMPTOMS) {
      this.createSymptomsSheet(data, wb, thinBorderStyle);
    } else if (testType === TestType.MENTAL_WELLBEING) {
      this.createTimedUpAndGoSheet(data, wb, thinBorderStyle);
    } else if (testType === TestType.GENERAL_WELLBEING) {
      this.createHealthStatusSheet(data, wb, thinBorderStyle);
    }

    await wb.writeP(path);
  }

  private createSymptomsSheet(data: ResultInterface, wb, thinBorderStyle) {
    const sheet = wb.addWorksheet('Symptoms Test Report');
    let row = 1;

    sheet
      .cell(row, 1)
      .string('Annotation')
      .style({ font: { bold: true, size: 16 } });
    row = row + 2;
    row = this.writeQuestionSymptoms(sheet, row);
    row = row + 1;

    data.symptoms.forEach((element) => {
      row = this.createSymptomsPatientSection(sheet, row, element);
      row = this.createSymptomsHeaders(sheet, row, thinBorderStyle);
      row = this.processSymptomsRecords(sheet, row, element, thinBorderStyle);
      row += 2;
    });
  }

  private createSymptomsPatientSection(sheet, row: number, element: any): number {
    sheet
      .cell(row, 1)
      .string(`Patient: ${element.username} (${element.nhsNumber})`)
      .style({ font: { bold: true, size: 20 } });
    row = row + 1;
    sheet
      .cell(row, 1)
      .string('Symptoms Test')
      .style({ font: { bold: true, size: 20 } });
    row = row + 2;
    return row;
  }

  private createSymptomsHeaders(sheet, row: number, thinBorderStyle): number {
    const headers = [
      'Username',
      'Date & Time',
      'Q1',
      'Q1b',
      'Q2',
      'Q2b',
      'Q2c',
      'Q3',
      'Q3b',
      'Q4',
      'Q5',
      'Q6',
      'Q6b',
      'Q7',
      'Red',
      'Amber',
      'Green',
      'Incomplete',
      'Flag?',
      'Notes',
    ];

    headers.forEach((header, index) => {
      sheet
        .cell(row, index + 1)
        .string(header)
        .style(thinBorderStyle)
        .style({
          font: { bold: true, size: fontSizeDefault },
          alignment: { horizontal: 'center' },
        });
    });

    return row + 1;
  }

  private processSymptomsRecords(sheet, row: number, element: any, thinBorderStyle): number {
    element.records.forEach((record) => {
      row = this.writeSymptomsRecordData(sheet, row, element, record, thinBorderStyle);
      row = this.writeSymptomsQuestions(sheet, row, record, thinBorderStyle);
      row = this.writeSymptomsRAGCounts(sheet, row, record, thinBorderStyle);
      row = this.writeSymptomsMetadata(sheet, row, record, thinBorderStyle);
      row++;
    });
    return row;
  }

  private writeSymptomsRecordData(sheet, row: number, element: any, record: any, thinBorderStyle): number {
    let currentColumn = 1;
    const startDate = record.startDate !== undefined ? dayjs(record.startDate).format('DD/MM/YYYY') : '';

    sheet
      .cell(row, currentColumn++)
      .string(element.username)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    sheet
      .cell(row, currentColumn++)
      .string(startDate + (record.status !== undefined && record.status === TestResultStatus.CANCELLED ? '*' : ''))
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    return currentColumn;
  }

  private writeSymptomsQuestions(sheet, row: number, record: any, thinBorderStyle): number {
    let currentColumn = 3; // Starting after username and date columns
    let totalReport = 12; // default if report = [] (array empty)
    if (record.report?.length > 0) {
      totalReport += 2;
    } // Q2 has 3 sub questions but only 1 question

    for (let i = 0; i < totalReport; i++) {
      const reportItem = record.report[i];

      if (reportItem?.code?.toLowerCase() === 'q3') {
        currentColumn++;
      }
      sheet
        .cell(row, currentColumn++)
        .string(reportItem?.answer || '')
        .style(thinBorderStyle)
        .style({
          font: { size: fontSizeDefault },
          alignment: { horizontal: 'center' },
        });
      if (reportItem?.code?.toLowerCase()?.includes('q2c')) {
        if (record.report?.length > 0) {
          currentColumn--;
        }
      }
    }
    return currentColumn;
  }

  private writeSymptomsRAGCounts(sheet, row: number, record: any, thinBorderStyle): number {
    const ragCounts = this.calculateRAGCounts(record.report);
    let currentColumn = 15; // Starting after question columns

    sheet
      .cell(row, currentColumn++)
      .number(ragCounts.red)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    sheet
      .cell(row, currentColumn++)
      .number(ragCounts.amber)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    sheet
      .cell(row, currentColumn++)
      .number(ragCounts.green)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    return currentColumn;
  }

  private calculateRAGCounts(report: any[]): { red: number; amber: number; green: number } {
    let red = 0;
    let amber = 0;
    let green = 0;

    report.forEach((reportItem) => {
      if (reportItem.rag === RAGType.A) {
        amber += 1;
      } else if (reportItem.rag === RAGType.G) {
        green += 1;
      } else if (reportItem.rag === RAGType.R) {
        red += 1;
      }
    });

    return { red, amber, green };
  }

  private writeSymptomsMetadata(sheet, row: number, record: any, thinBorderStyle): number {
    let currentColumn = 18; // Starting after RAG columns

    // Incomplete
    sheet
      .cell(row, currentColumn++)
      .string(record.status !== undefined && record.status === TestResultStatus.CANCELLED ? 'Yes' : '')
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    // Flag
    sheet
      .cell(row, currentColumn++)
      .string(record.redFlag ? 'Yes' : '')
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    // Notes
    sheet
      .cell(row, currentColumn++)
      .string(this.formatNotesWithAcknowledgedBy(record, '\r\n'))
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    return currentColumn;
  }

  private createTimedUpAndGoSheet(data: ResultInterface, wb, thinBorderStyle) {
    const sheet = wb.addWorksheet('Timed Up and Go Test Report');
    let row = 1;

    data.timeUpAndGo.forEach((element) => {
      row = this.createTimedUpAndGoPatientSection(sheet, row, element);
      row = this.createTimedUpAndGoHeaders(sheet, row, thinBorderStyle);
      row = this.processTimedUpAndGoRecords(sheet, row, element, thinBorderStyle);
      row = row + 2;
    });
  }

  private createTimedUpAndGoPatientSection(sheet, row: number, element: any): number {
    sheet
      .cell(row, 1)
      .string(`Patient: ${element.username} (${element.nhsNumber})`)
      .style({ font: { bold: true, size: 20 } });
    row = row + 1;
    sheet
      .cell(row, 1)
      .string('Timed Up & Go Test')
      .style({ font: { bold: true, size: 20 } });
    row = row + 2;
    sheet
      .cell(row, 1)
      .string('Test Report')
      .style({ font: { bold: true, size: 16 } });
    row = row + 1;
    return row;
  }

  private createTimedUpAndGoHeaders(sheet, row: number, thinBorderStyle): number {
    const headers = [
      { text: 'Username', size: fontSizeDefault },
      { text: 'Date & Time', size: undefined },
      { text: 'Time', size: undefined },
      { text: 'Cancelled', size: undefined },
      { text: 'Flag?', size: undefined },
      { text: 'Notes', size: undefined },
    ];

    headers.forEach((header, index) => {
      sheet
        .cell(row, index + 1)
        .string(header.text)
        .style(thinBorderStyle)
        .style({
          font: { bold: true, size: header.size },
          alignment: { horizontal: 'center' },
        });
    });

    return row + 1;
  }

  private processTimedUpAndGoRecords(sheet, row: number, element: any, thinBorderStyle): number {
    element.records.forEach((record) => {
      row = this.writeTimedUpAndGoRecordData(sheet, row, element, record, thinBorderStyle);
      row++;
    });
    return row;
  }

  private writeTimedUpAndGoRecordData(sheet, row: number, element: any, record: any, thinBorderStyle): number {
    let currentColumn = 1;
    const startDate = record.startDate !== undefined ? dayjs(record.startDate).format('DD/MM/YYYY') : '';

    sheet
      .cell(row, currentColumn++)
      .string(element.username)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    sheet
      .cell(row, currentColumn++)
      .string(startDate + (record.status !== undefined && record.status === TestResultStatus.CANCELLED ? '*' : ''))
      .style(thinBorderStyle);

    // ttc
    sheet
      .cell(row, currentColumn++)
      .number(record.ttc || 0)
      .style({ alignment: { horizontal: 'center' } })
      .style(thinBorderStyle);

    // Incomplete
    sheet
      .cell(row, currentColumn++)
      .string(record.status !== undefined && record.status === TestResultStatus.CANCELLED ? 'Yes' : '')
      .style({ alignment: { horizontal: 'center' } })
      .style(thinBorderStyle);

    // Flag
    sheet
      .cell(row, currentColumn++)
      .string(record.redFlag ? 'Yes' : '')
      .style({ alignment: { horizontal: 'center' } })
      .style(thinBorderStyle);

    // Notes
    sheet.cell(row, currentColumn++).string(this.formatNotesWithAcknowledgedBy(record)).style(thinBorderStyle);

    return row;
  }

  private createHealthStatusSheet(data: ResultInterface, wb, thinBorderStyle) {
    const sheet = wb.addWorksheet('KCCQ Test Report');
    let row = 1;

    sheet
      .cell(row, 1)
      .string('Annotation')
      .style({ font: { bold: true, size: 16 } });
    row = row + 2;
    row = this.writeQuestionHealthStatus(sheet, row);
    row = row + 1;

    data.healthStatus.forEach((element) => {
      row = this.createHealthStatusPatientSection(sheet, row, element);
      row = this.createHealthStatusHeaders(sheet, row, thinBorderStyle);
      row = this.processHealthStatusRecords(sheet, row, element, thinBorderStyle);
      row += 2;
    });
  }

  private createHealthStatusPatientSection(sheet, row: number, element: any): number {
    sheet
      .cell(row, 1)
      .string(`Patient: ${element.username} (${element.nhsNumber})`)
      .style({ font: { bold: true, size: 20 } });
    row = row + 1;
    sheet
      .cell(row, 1)
      .string('KCCQ Test')
      .style({ font: { bold: true, size: 20 } });
    row = row + 2;
    return row;
  }

  private createHealthStatusHeaders(sheet, row: number, thinBorderStyle): number {
    const headers = [
      'Username',
      'Date & Time',
      'Q1A',
      'Q1B',
      'Q1C',
      'Q1D',
      'Q1E',
      'Q1F',
      'Q2',
      'Q3',
      'Q4',
      'Q5',
      'Q6',
      'Q7',
      'Q8',
      'Q9',
      'Q10',
      'Q11',
      'Q12',
      'Q13',
      'Q14',
      'Q15A',
      'Q15B',
      'Q15C',
      'Q15D',
      'Score',
      'Incomplete',
      'Flag?',
      'Notes',
    ];

    headers.forEach((header, index) => {
      sheet
        .cell(row, index + 1)
        .string(header)
        .style(thinBorderStyle)
        .style({
          font: { bold: true, size: fontSizeDefault },
          alignment: { horizontal: 'center' },
        });
    });

    return row + 1;
  }

  private processHealthStatusRecords(sheet, row: number, element: any, thinBorderStyle): number {
    element.records.forEach((record) => {
      row = this.writeHealthStatusRecordData(sheet, row, element, record, thinBorderStyle);
      row = this.writeHealthStatusQuestions(sheet, row, record, thinBorderStyle);
      row = this.writeHealthStatusMetadata(sheet, row, record, thinBorderStyle);
      row++;
    });
    return row;
  }

  private writeHealthStatusRecordData(sheet, row: number, element: any, record: any, thinBorderStyle): number {
    let currentColumn = 1;
    const startDate = record.startDate !== undefined ? dayjs(record.startDate).format('DD/MM/YYYY') : '';

    sheet
      .cell(row, currentColumn++)
      .string(element.username)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    sheet
      .cell(row, currentColumn++)
      .string(startDate + (record.status !== undefined && record.status === TestResultStatus.CANCELLED ? '*' : ''))
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    return currentColumn;
  }

  private writeHealthStatusQuestions(sheet, row: number, record: any, thinBorderStyle): number {
    let currentColumn = 3; // Starting after username and date columns
    const totalReport = 23; // default if report = [] (array empty)

    for (let i = 0; i < totalReport; i++) {
      const reportItem = record.report[i];

      sheet
        .cell(row, currentColumn++)
        .number(reportItem?.score || 0)
        .style(thinBorderStyle)
        .style({
          font: { size: fontSizeDefault },
          alignment: { horizontal: 'center' },
        });
    }

    sheet
      .cell(row, currentColumn++)
      .number(record.totalScores || 0)
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    return currentColumn;
  }

  private writeHealthStatusMetadata(sheet, row: number, record: any, thinBorderStyle): number {
    let currentColumn = 26; // Starting after score columns

    // Incomplete
    sheet
      .cell(row, currentColumn++)
      .string(record.status !== undefined && record.status === TestResultStatus.CANCELLED ? 'Yes' : '')
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    // Flag
    sheet
      .cell(row, currentColumn++)
      .string(record.redFlag ? 'Yes' : '')
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    // Notes
    sheet
      .cell(row, currentColumn++)
      .string(this.formatNotesWithAcknowledgedBy(record))
      .style(thinBorderStyle)
      .style({
        font: { size: fontSizeDefault },
        alignment: { horizontal: 'center' },
      });

    return currentColumn;
  }

  private writeQuestionSymptoms(sheet, rowCurrent: number) {
    const alphabets = 'abcdefghijklmnopqrstuvwxyz'.split('');
    let row = rowCurrent;
    const questions = this.getSymptomsQuestions();

    for (const question of questions) {
      row = this.writeSymptomsQuestion(sheet, row, question);
      row = this.writeSymptomsAnswers(sheet, row, question.answers, alphabets);
      row = row + 1;
    }

    return row;
  }

  private getSymptomsQuestions() {
    const rawdata = fs.readFileSync(seeder.testDataPath);
    const records = JSON.parse(rawdata) as any[];
    return records.filter((record) => {
      return record.title === 'Symptoms';
    })[0]?.questions as Array<{
      text: string;
      code: string;
      answers: Array<{
        questions: Array<{
          text: string;
          code: string;
          answers: Array<{ text: string; code: string; questions: any[] }>;
        }>;
      }>;
    }>;
  }

  private writeSymptomsQuestion(sheet, row: number, question: any): number {
    sheet
      .cell(row, 1)
      .string(question.code)
      .style({ font: { bold: true, size: fontSizeDefault } });

    sheet
      .cell(row, 4)
      .string(question.text)
      .style({ font: { bold: true, size: fontSizeDefault } });

    return row + 1;
  }

  private writeSymptomsAnswers(sheet, row: number, answers: any[], alphabets: string[]): number {
    for (const answer of answers) {
      const subQuestions = answer.questions || [];
      if (subQuestions.length === 0) {
        continue;
      }

      row = this.writeSymptomsSubQuestions(sheet, row, subQuestions, alphabets);
    }

    return row;
  }

  private writeSymptomsSubQuestions(sheet, row: number, subQuestions: any[], alphabets: string[]): number {
    let subQuestionIndex = 0;

    for (const subQuestion of subQuestions) {
      const subAnswers = subQuestion.answers || [];

      if (subAnswers.length === 0) {
        continue;
      }

      row = this.writeSymptomsSubQuestion(sheet, row, subQuestion, alphabets[subQuestionIndex]);
      row = this.writeSymptomsSubAnswers(sheet, row, subAnswers, subQuestions);
      subQuestionIndex++;
    }

    return row;
  }

  private writeSymptomsSubQuestion(sheet, row: number, subQuestion: any, alphabet: string): number {
    sheet
      .cell(row, 2)
      .string(alphabet)
      .style({ font: { size: fontSizeDefault } });

    sheet
      .cell(row, 4)
      .string(subQuestion.text)
      .style({ font: { size: fontSizeDefault } });

    return row + 1;
  }

  private writeSymptomsSubAnswers(sheet, row: number, subAnswers: any[], subQuestions: any[]): number {
    for (const subAnswerIndex in subAnswers) {
      const subAnswer = subAnswers[subAnswerIndex];

      this.handleSubSubQuestions(subAnswer, subAnswerIndex, subQuestions);
      row = this.writeSymptomsSubAnswer(sheet, row, subAnswer);
    }

    return row;
  }

  private handleSubSubQuestions(subAnswer: any, subAnswerIndex: string, subQuestions: any[]): void {
    const subSubQuestion = subAnswer.questions || [];

    if (+subAnswerIndex === 0 && subSubQuestion.length !== 0) {
      subQuestions.push(subAnswer.questions[0]);
    }
  }

  private writeSymptomsSubAnswer(sheet, row: number, subAnswer: any): number {
    sheet
      .cell(row, 3)
      .string(subAnswer.code)
      .style({ font: { size: fontSizeDefault } });

    sheet
      .cell(row, 4)
      .string(this.cleanSymptomsAnswerText(subAnswer.text))
      .style({ font: { size: fontSizeDefault } });

    return row + 1;
  }

  private cleanSymptomsAnswerText(text: string): string {
    return text
      .replace(/<b>/g, '')
      .replace(/<\/b>/g, '')
      .replace(/<br\/>/g, '');
  }

  private writeQuestionHealthStatus(sheet, rowCurrent: number) {
    const alphabets = 'abcdefghijklmnopqrstuvwxyz'.split('');
    let row = rowCurrent;
    const rawdata = fs.readFileSync(seeder.testDataPath);
    const records = JSON.parse(rawdata) as any[];
    const questions = this.getHealthStatusQuestions(records);

    for (const questionIndex in questions) {
      const question = questions[questionIndex];
      row = this.writeQuestion(sheet, row, question, +questionIndex + 1);
      row = this.writeQuestionContent(sheet, row, question, alphabets);
      row = row + 1;
    }

    return row;
  }

  private getHealthStatusQuestions(records: any[]) {
    return (records.filter((record) => {
      return record.title === 'Health Status';
    })[0]?.questions || []) as Array<{
      text: string;
      questions: Array<{
        text: string;
        code: string;
        answers: Array<{ text: string; score: number }>;
      }>;
      answers: Array<{ text: string; score: number }>;
    }>;
  }

  private writeQuestion(sheet, row: number, question: any, questionNumber: number): number {
    sheet
      .cell(row, 1)
      .string(`Q${questionNumber}`)
      .style({ font: { bold: true, size: fontSizeDefault } });

    sheet
      .cell(row, 4)
      .string(this.cleanQuestionText(question.text))
      .style({ font: { bold: true, size: fontSizeDefault } });

    return row + 1;
  }

  private writeQuestionContent(sheet, row: number, question: any, alphabets: string[]): number {
    const subQuestions = question.questions || [];
    const answers = question.answers || [];

    if (subQuestions.length === 0 && answers.length === 0) {
      return row;
    }

    if (subQuestions.length !== 0) {
      return this.writeSubQuestions(sheet, row, subQuestions, alphabets);
    }

    if (answers.length !== 0) {
      return this.writeAnswers(sheet, row, answers);
    }

    return row;
  }

  private writeSubQuestions(sheet, row: number, subQuestions: any[], alphabets: string[]): number {
    for (const subQuestionIndex in subQuestions) {
      const subQuestion = subQuestions[subQuestionIndex];

      sheet
        .cell(row, 2)
        .string(alphabets[subQuestionIndex].toUpperCase())
        .style({ font: { size: fontSizeDefault } });

      sheet
        .cell(row, 4)
        .string(subQuestion.text)
        .style({ font: { size: fontSizeDefault } });

      row = row + 1;

      const isLastSubQuestion = +subQuestionIndex === subQuestions.length - 1;
      if (isLastSubQuestion) {
        row = this.writeSubAnswers(sheet, row, subQuestion.answers || []);
      }
    }

    return row;
  }

  private writeSubAnswers(sheet, row: number, subAnswers: any[]): number {
    if (subAnswers.length === 0) {
      return row;
    }

    for (const subAnswerIndex in subAnswers) {
      const subAnswer = subAnswers[subAnswerIndex];

      sheet
        .cell(row, 3)
        .number(+subAnswerIndex + 1)
        .style({
          font: { size: fontSizeDefault },
          alignment: { horizontal: 'left' },
        });

      sheet
        .cell(row, 4)
        .string(this.cleanAnswerText(subAnswer.text))
        .style({ font: { size: fontSizeDefault } });

      row = row + 1;
    }

    return row;
  }

  private writeAnswers(sheet, row: number, answers: any[]): number {
    for (const answerIndex in answers) {
      const answer = answers[answerIndex];

      sheet
        .cell(row, 3)
        .number(+answerIndex + 1)
        .style({
          font: { size: fontSizeDefault },
          alignment: { horizontal: 'left' },
        });

      sheet
        .cell(row, 4)
        .string(this.cleanAnswerText(answer.text))
        .style({ font: { size: fontSizeDefault } });

      row = row + 1;
    }

    return row;
  }

  private cleanQuestionText(text: string): string {
    return text
      .replace(/<b>/g, '')
      .replace(/<\/b>/g, '')
      .replace(/<u>/g, '')
      .replace(/<\/u>/g, '')
      .replace(/<br\/>/g, '');
  }

  private cleanAnswerText(text: string): string {
    return text.replace(/<b>/g, '').replace(/<\/b>/g, '');
  }

  private formatNotesWithAcknowledgedBy(record: any, separator: string = ' - '): string {
    // Handle undefined notes properly
    // Requirements specify: "Empty fields must be represented by a blank cell"
    if (record.notes === undefined || record.notes === null || record.notes === '') {
      return '';
    }

    const acknowledgedByPrefix = !!record?.acknowledgedBy?.username ? record.acknowledgedBy.username + separator : '';

    return acknowledgedByPrefix + record.notes;
  }

  private generatePatientInfoSheet(patient: any): string[] {
    const csvLines = [];

    // Header row (bold formatting will be handled by frontend/Excel)
    csvLines.push('Patient info');
    csvLines.push('');

    // Patient info headers
    const headers = [
      'Patient Username',
      'First Name',
      'Surname',
      'DOB',
      'Age',
      'Email address',
      'NHS Number',
      'Mobile Number',
      'Ethnicity',
      'Smoking Status',
      'Inflammatory Bowel Disease sub-type',
      'Year of diagnosis',
      'Current EDD',
      'Parity',
      'Current meds',
      'Last disease activity',
      'Previous meds',
      'Medical history/Comorbidities',
      'Last FCP (Fecal Calprotectin)',
    ];

    csvLines.push(headers.join(','));

    // Calculate age from date of birth
    const age = patient.dateOfBirth ? dayjs().diff(dayjs(patient.dateOfBirth), 'year') : '';

    // Format IBD subtype display name
    const ibdSubtypeDisplay =
      patient.ibdSubtype === IBDSubtype.CROHNS_DISEASE
        ? "Crohn's disease"
        : patient.ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS
          ? 'Ulcerative Colitis'
          : '';

    // Format expected due date
    const currentEDD = patient.expectedDueDate ? dayjs(patient.expectedDueDate).utc().format('YYYY-MM-DD') : '';

    // Format year of diagnosis
    const yearOfDiagnosis = patient.yearOfDiagnosis ? dayjs(patient.yearOfDiagnosis).utc().format('YYYY-MM-DD') : '';

    // Format last disease activity
    const lastDiseaseActivity = patient.lastDiseaseActivity
      ? dayjs(patient.lastDiseaseActivity).utc().format('YYYY-MM-DD')
      : '';

    // Format last FCP with "Date of - Result" format
    let lastFcp = '';
    if (patient.lastFcpDate && patient.lastFcpResult) {
      const fcpDate = dayjs(patient.lastFcpDate).utc().format('YYYY-MM-DD');
      lastFcp = `${fcpDate} - ${patient.lastFcpResult}`;
    } else if (patient.lastFcpDate) {
      const fcpDate = dayjs(patient.lastFcpDate).utc().format('YYYY-MM-DD');
      lastFcp = `${fcpDate} - `;
    } else if (patient.lastFcpResult) {
      lastFcp = ` - ${patient.lastFcpResult}`;
    }

    // Patient data row
    const patientData = [
      patient.username || '',
      patient.firstName || '',
      patient.lastName || '',
      patient.dateOfBirth ? dayjs(patient.dateOfBirth).utc().format('YYYY-MM-DD') : '',
      age.toString(),
      patient.email || '',
      patient.nhsNumber || '',
      patient.mobileNumber || '',
      patient.ethnicity || '',
      patient.smokingStatus || '',
      ibdSubtypeDisplay,
      yearOfDiagnosis,
      currentEDD,
      patient.parity || '',
      patient.currentMeds || '',
      lastDiseaseActivity,
      patient.previousMeds || '',
      patient.medicalHistory || '',
      lastFcp,
    ];

    // Escape commas and quotes in data
    const escapedData = patientData.map((field) => {
      if (field.includes(',') || field.includes('"') || field.includes('\n')) {
        return `"${field.replace(/"/g, '""')}"`;
      }
      return field;
    });

    csvLines.push(escapedData.join(','));
    csvLines.push(''); // Empty line after patient info sheet

    return csvLines;
  }

  private generateIndividualPatientSymptomsSheet(patient: any, testResults: any[]): string[] {
    const csvLines = [];

    // Determine IBD subtype for question columns and sheet naming
    const ibdSubtype =
      patient.ibdSubtype === IBDSubtype.CROHNS_DISEASE
        ? SymptomsTestSubtype.HBI
        : patient.ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS
          ? SymptomsTestSubtype.SCCAI
          : '';

    csvLines.push('');
    csvLines.push(`Test 1 - Symptoms`);
    csvLines.push('');

    const firstRow = testResults?.[0];
    if (!firstRow) {
      return csvLines;
    }

    // Headers for symptoms sheet (removed Username and NHS number)
    const baseHeaders = [
      'Date & Time',
      'Red Flags',
      'Amber Flags',
      'Green flags',
      'Incomplete',
      'Flag',
      'Final Score',
      'Notes',
    ];

    // Add question columns based on IBD subtype
    const questionHeaders = Array.isArray(firstRow?.report)
      ? firstRow.report.map((item: any, index: number) => `"Q${index + 1} - ${item.question}"`)
      : [];

    const allHeaders = [...baseHeaders, ...questionHeaders];
    csvLines.push(allHeaders.join(','));

    // Generate sheet headers with test numbering
    for (const testResult of testResults) {
      // Data row for this test result
      const isIncomplete = testResult.status !== TestResultStatus.COMPLETED;
      const hasFlag = testResult.redFlag || testResult.reds > 0 || testResult.ambers > 0;

      const baseData = [
        testResult.completedDate ? dayjs(testResult.completedDate).utc().format('YYYY-MM-DD HH:mm:ss') : '',
        testResult.reds || 0,
        testResult.ambers || 0,
        testResult.greens || 0,
        isIncomplete ? 'Yes' : 'No',
        hasFlag ? 'Yes' : 'No',
        testResult.totalScores || '',
        testResult?.notes || '',
      ];

      // Add question answers with human-readable text conversion
      const questionData = [];
      const maxQuestions = ibdSubtype === SymptomsTestSubtype.HBI ? 6 : 8;
      if (testResult.report && Array.isArray(testResult.report)) {
        const processedQuestions = this.processSymptomsQuestionsForCSV(testResult.report, ibdSubtype);

        for (let i = 1; i <= maxQuestions; i++) {
          const questionKey = `q${i}`;
          questionData.push(processedQuestions[questionKey] || '');
        }
      } else {
        // Fill with empty values if no report data
        for (let i = 0; i < maxQuestions; i++) {
          questionData.push('');
        }
      }

      const allData = [...baseData, ...questionData];

      // Escape commas and quotes in data
      const escapedData = allData.map((field) => {
        const fieldStr = field.toString();
        if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
          return `"${fieldStr.replace(/"/g, '""')}"`;
        }
        return fieldStr;
      });

      csvLines.push(escapedData.join(','));
      csvLines.push(''); // Empty line after each test result
    }

    return csvLines;
  }

  private generateIndividualPatientMentalWellbeingSheet(_patient: any, testResults: any[]): string[] {
    const csvLines = [];

    csvLines.push('');
    csvLines.push(`Test 2 - Mental Wellbeing`);
    csvLines.push('');

    const firstRow = testResults?.[0];
    if (!firstRow) {
      return csvLines;
    }

    // Headers for mental wellbeing sheet (removed Username and NHS number)
    const headers = [
      'Date & Time',
      'Incomplete',
      'Flag',
      'HADS Anxiety Score',
      'HADS Depression Score',
      'Notes',
      // Add question columns for mental wellbeing questions
      ...(Array.isArray(firstRow?.report)
        ? firstRow.report.map((item: any, index: number) => `"Q${index + 1} - ${item.question}"`)
        : []),
    ];

    csvLines.push(headers.join(','));
    for (const testResult of testResults) {
      // Data row for this test result
      const isIncomplete = testResult.status !== TestResultStatus.COMPLETED;
      const hasFlag = testResult.redFlag || testResult.anxietyScore >= 11 || testResult.depressionScore >= 11;

      const baseData = [
        testResult.completedDate ? dayjs(testResult.completedDate).utc().format('YYYY-MM-DD HH:mm:ss') : '',
        isIncomplete ? 'Yes' : 'No',
        hasFlag ? 'Yes' : 'No',
        testResult.anxietyScore || '',
        testResult.depressionScore || '',
        testResult?.notes || '',
      ];

      // Add question answers with human-readable text conversion for mental wellbeing
      const questionData = [];
      const maxQuestions = 14;
      if (testResult.report && Array.isArray(testResult.report)) {
        const processedQuestions = this.processMentalWellbeingQuestionsForCSV(testResult.report);
        for (let i = 1; i <= maxQuestions; i++) {
          const questionKey = `q${i}`;
          questionData.push(processedQuestions[questionKey] || '');
        }
      } else {
        // Fill with empty values if no report data
        for (let i = 0; i < maxQuestions; i++) {
          questionData.push('');
        }
      }

      const allData = [...baseData, ...questionData];

      // Escape commas and quotes in data
      const escapedData = allData.map((field) => {
        const fieldStr = field.toString();
        if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
          return `"${fieldStr.replace(/"/g, '""')}"`;
        }
        return fieldStr;
      });

      csvLines.push(escapedData.join(','));
      csvLines.push(''); // Empty line after each test result
    }

    return csvLines;
  }

  private generateIndividualPatientGeneralWellbeingSheet(_patient: any, testResults: any[]): string[] {
    const csvLines = [];
    csvLines.push('');
    csvLines.push(`Test 3 - General Wellbeing`);
    csvLines.push('');

    const firstRow = testResults?.[0];
    if (!firstRow) {
      return csvLines;
    }

    // Headers for general wellbeing sheet (removed Username and NHS number)
    const baseHeaders = [
      'Date & Time',
      'Incomplete',
      'Flag',
      'Overdue Flag',
      'SIBDQ Score',
      'EQ Score',
      'Notes',
      // Add question columns for general wellbeing questions
      ...(Array.isArray(firstRow?.report)
        ? firstRow.report.map((item: any, index: number) => `"Q${index + 1} - ${item.question}"`)
        : []),
    ];
    // Add additional blank columns for manual clinician input as specified in requirements
    const additionalColumns = [
      '12 week scan outcomes',
      '20 week scan outcomes',
      'VTE during pregnancy',
      'VTE prophylaxis given',
      'Birth outcomes',
      'Birth weight',
      'Birth outcomes',
      'Mode of delivery',
      'Complications',
      'Gestational size',
      'Gestational age',
      'IBD outcomes',
      'Steroids during pregnancy',
      'Started biologic during pregnancy',
    ];

    const allHeaders = [...baseHeaders, ...additionalColumns];
    csvLines.push(allHeaders.join(','));

    // Generate sheet headers with test numbering
    for (const testResult of testResults) {
      // Data row for this test result
      const isIncomplete = testResult.status !== TestResultStatus.COMPLETED;
      const hasFlag = testResult.redFlag;
      const isOverdue = testResult.status === TestResultStatus.OVERDUE;

      const baseData = [
        testResult.completedDate ? dayjs(testResult.completedDate).utc().format('YYYY-MM-DD HH:mm:ss') : '',
        isIncomplete ? 'Yes' : 'No',
        hasFlag ? 'Yes' : 'No',
        isOverdue ? 'Yes' : 'No',
        testResult.sibdqScore || '',
        testResult.eqScore || '',
        testResult?.notes || '',
      ];

      // Add question answers with human-readable text conversion for general wellbeing
      const questionData = [];
      if (testResult.report && Array.isArray(testResult.report)) {
        const processedQuestions = this.processGeneralWellbeingQuestionsForCSV(testResult.report);
        for (let i = 1; i <= 22; i++) {
          const questionKey = `q${i}`;
          questionData.push(processedQuestions[questionKey] || '');
        }
      } else {
        // Fill with empty values if no report data
        for (let i = 0; i < 22; i++) {
          questionData.push('');
        }
      }

      // Add blank columns for manual clinician input
      const blankColumns = new Array(additionalColumns.length).fill('');

      const allData = [...baseData, ...questionData, ...blankColumns];

      // Escape commas and quotes in data
      const escapedData = allData.map((field) => {
        const fieldStr = field.toString();
        if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
          return `"${fieldStr.replace(/"/g, '""')}"`;
        }
        return fieldStr;
      });

      csvLines.push(escapedData.join(','));
      csvLines.push(''); // Empty line after each test result
    }

    return csvLines;
  }

  private combineMultiSheetCSV(sheets: string[][]): string {
    const allLines = [];

    // Combine all sheets into a single CSV content
    for (const sheet of sheets) {
      allLines.push(...sheet);
    }

    return allLines.join('\n');
  }
}
