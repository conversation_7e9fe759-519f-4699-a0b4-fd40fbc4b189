import { AosCollections, getCollectionToken } from '@aos-database/database.constant';
import { OverdueThreshold, SymptomsTestSubtype } from '@aos-enum/flag-threshold.enum';
import { IBDSubtype } from '@aos-enum/ibd-subtype.enum';
import { NumericQuestionCode, TextQuestionCode } from '@aos-enum/numeric-question.enum';
import { RAGType } from '@aos-enum/rag-type.enum';
import { TestResultStatus } from '@aos-enum/test-result-status.enum';
import { TestTitle } from '@aos-enum/test-title.enum';
import { TestType } from '@aos-enum/test-type.enum';
import { UserRole } from '@aos-enum/user-role.enum';
import { enumValuesToArray } from '@aos-shared/functions/array.function';
import { BadRequestException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import * as crypto from 'crypto';
import * as moment from 'moment';
import { Model, Types } from 'mongoose';
import NumberHelper from 'shared/helpers/number.helper';
import PaginationHelper from 'shared/helpers/pagination.heper';
import { MESSAGES } from '../../shared/constants/error-messages.constant';
import { PaginationRO } from '../../shared/interfaces/pagination.interface';
import { FirebaseService } from '../../shared/services/firebase.service';
import { NotificationSchedulerService } from '../../shared/services/notification-scheduler.service';
import { TestService } from '../tests/test.service';
import { CalculatorService } from './calculator.service';
import {
  AcknowledgeOverdueTestWithNotesDto,
  AcknowledgeRedFlagDto,
  GetOverdueTestsDto,
  GetPatientDetailsDto,
} from './dto';
import { ChartQueryDto, SingleTestChartQueryDto } from './dto/chart-query.dto';
import { FlagLoggingService } from './flag-logging.service';
import {
  OverdueTestResultRO,
  OverdueTestsWithPagination,
  PatientDetailsWithPagination,
  PatientTestResultRO,
  QuestionBreakdownRO,
  RAGBreakdownRO,
  TestResultsWithPagination,
} from './interface';
import {
  GeneralWellbeingChartData,
  MentalWellbeingChartData,
  PatientChartDataRO,
  PatientHeaderInfo,
  RawTestResultForChart,
  SingleTestChartDataRO,
  SymptomsChartData,
  TestResultTableData,
  TestResultTableRow,
} from './interface/chart-data.interface';
import { OVERDUE_INTERVAL_DAYS } from '@aos-shared/constants/overdue';

interface QuestionValidationContext {
  question: any;
  updateData: any;
  submittedAnswer: any;
}

interface AnswerSearchResult {
  selectedAnswerId?: string;
  hasSelectedAnswer?: boolean;
  textValue?: string;
  numericValue?: number;
}

@Injectable()
export class TestResultService {
  constructor(
    @Inject(getCollectionToken(AosCollections.TestResult))
    private readonly testResultModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.TestTimedUpHistory))
    private readonly testTimedUpHistoryModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.TestKccqHistory))
    private readonly testKccqHistoryModel: Model<any>,
    @Inject(getCollectionToken(AosCollections.User))
    private readonly userModel: Model<any>,
    private readonly testService: TestService,
    private readonly firebaseService: FirebaseService,
    private readonly calculatorService: CalculatorService,
    private readonly flagLoggingService: FlagLoggingService,
    private readonly notificationSchedulerService: NotificationSchedulerService,
  ) {}

  private readonly logger = new Logger(TestResultService.name);

  public async getFlagTestResults(query): Promise<TestResultsWithPagination> {
    const { orderBy, orderType, paginationParam } = this.buildQueryParams(query);
    const sortCondition = this.buildFlagTestResultsSortCondition(orderBy, orderType);

    return await this.buildGetTestResultQuery(
      {
        redFlag: true,
        acknowledged: false,
        status: TestResultStatus.COMPLETED,
      },
      sortCondition,
      paginationParam,
    );
  }

  private buildQueryParams(query) {
    const orderBy =
      query.orderBy === undefined ? 'startDate' : query.orderBy === 'username' ? 'patient.username' : query.orderBy;

    const orderType = query.orderType === undefined || query.orderType === 'desc' ? -1 : 1;
    const paginationParam = PaginationHelper.buildPaginationQueryParams(query.page || 1, query.limit || 10);

    return { orderBy, orderType, paginationParam };
  }

  private buildFlagTestResultsSortCondition(orderBy: string, orderType: number): any {
    if (orderBy === 'patient.username') {
      return { [orderBy]: orderType, startDate: -1 };
    } else if (orderBy === 'title') {
      return {
        ['type']: orderType,
        'patient.username': 1,
        startDate: -1,
      };
    } else {
      return { [orderBy]: orderType };
    }
  }

  public async getReportByType(query): Promise<TestResultsWithPagination> {
    const testResultType = query.type || TestType.SYMPTOMS;
    const { orderBy, orderType, paginationParam } = this.buildReportQueryParams(query);
    const sortCondition = this.buildReportSortCondition(orderBy, orderType);

    return await this.buildGetTestResultQuery(
      {
        type: parseInt(testResultType),
        status: { $ne: TestResultStatus.NEW },
      },
      sortCondition,
      paginationParam,
    );
  }

  private buildReportQueryParams(query) {
    let orderBy = query.orderBy === undefined ? 'completedDate' : query.orderBy;
    const orderType = query.orderType === undefined || query.orderType === 'desc' ? -1 : 1;
    const paginationParam = PaginationHelper.buildPaginationQueryParams(query.page || 1, query.limit || 10);

    // Map frontend field names to database/computed field names
    const fieldMapping = {
      username: 'patient.username',
      c: 'status', // Legacy support
      subtype: 'computedSubtype',
      completed: 'computedCompleted',
      overdue: 'computedOverdue',
    };

    if (fieldMapping[orderBy]) {
      orderBy = fieldMapping[orderBy];
    }

    return { orderBy, orderType, paginationParam };
  }

  private buildReportSortCondition(orderBy: string, orderType: number): any {
    // Handle special sorting cases
    if (orderBy === 'patient.username') {
      return { [orderBy]: orderType, completedDate: -1 };
    } else if (orderBy === 'completedDate') {
      return { [orderBy]: orderType };
    } else if (orderBy === 'computedSubtype') {
      // Sort by subtype, then by username, then by completedDate
      return {
        [orderBy]: orderType,
        'patient.username': 1,
        completedDate: -1,
      };
    } else if (orderBy === 'computedCompleted' || orderBy === 'computedOverdue') {
      // Sort by boolean status, then by username, then by completedDate
      return {
        [orderBy]: orderType,
        'patient.username': 1,
        completedDate: -1,
      };
    } else {
      // Default sorting for other fields
      return {
        [orderBy]: orderType,
        'patient.username': 1,
        completedDate: -1,
      };
    }
  }

  public async getPatientTestResults(id, testResultType, query): Promise<TestResultsWithPagination> {
    const { orderBy, orderType, paginationParam } = this.buildReportQueryParams(query);

    const sortCondition = this.buildReportSortCondition(orderBy, orderType);

    const result = await this.buildGetTestResultQuery(
      {
        type: parseInt(testResultType),
        patient: new Types.ObjectId(id),
        status: { $ne: TestResultStatus.NEW },
      },
      sortCondition,
      paginationParam,
    );

    result.baseScore = await this.calculateBaseScore(id, testResultType);

    return result;
  }

  private buildSortCondition(orderBy: string, orderType: number): any {
    if (orderBy === 'patient.username') {
      return { [orderBy]: orderType, startDate: -1 };
    } else if (orderBy === 'title') {
      return {
        [orderBy]: orderType,
        'patient.username': 1,
        startDate: -1,
      };
    } else {
      return { [orderBy]: orderType };
    }
  }

  private async calculateBaseScore(id: string, testResultType: number): Promise<number> {
    const scoreCalculators = {
      [TestType.SYMPTOMS]: () => this.getSymptomsBaseScore(id),
      [TestType.MENTAL_WELLBEING]: () => this.getMentalWellbeingBaseScore(id),
      [TestType.GENERAL_WELLBEING]: () => this.getGeneralWellbeingBaseScore(id),
    };

    const calculator = scoreCalculators[testResultType];
    return calculator ? await calculator() : 0;
  }

  private async getSymptomsBaseScore(id: string): Promise<number> {
    const firstRecord = await this.testResultModel
      .findOne({
        patient: new Types.ObjectId(id),
        status: TestResultStatus.COMPLETED,
      })
      .sort({ createdAt: 1 });

    return firstRecord ? firstRecord.totalScores : 0;
  }

  private async getMentalWellbeingBaseScore(id: string): Promise<number> {
    const firstRecord = await this.testTimedUpHistoryModel
      .findOne({
        patient: new Types.ObjectId(id),
      })
      .sort({ createdAt: 1 });

    return firstRecord ? firstRecord.timeScore : 0;
  }

  private async getGeneralWellbeingBaseScore(id: string): Promise<number> {
    const firstRecord = await this.testKccqHistoryModel
      .findOne({
        patient: new Types.ObjectId(id),
      })
      .sort({ createdAt: 1 });

    return firstRecord ? firstRecord.totalScores : 0;
  }

  /**
   * Unified acknowledge for overdue tests with notes (atomic operation)
   * Replaces both separate add-notes and acknowledge operations as required by functional requirements 6.4.3.2
   */
  public async acknowledgeOverdueTest(
    id: string,
    user: any,
    updateData: AcknowledgeOverdueTestWithNotesDto,
  ): Promise<void> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException(MESSAGES.INVALID_TEST_RESULT_ID);
    }

    const testResult = await this.testResultModel.findById(id);

    if (!testResult) {
      throw new NotFoundException('Test result not found');
    }

    if (testResult.status !== TestResultStatus.OVERDUE) {
      throw new BadRequestException(MESSAGES.TEST_NOT_OVERDUE);
    }

    // Check if notes have already been saved (immutable once saved)
    if (testResult.notesSaved) {
      throw new BadRequestException(MESSAGES.NOTES_CANNOT_BE_EDITED);
    }

    // Atomic operation: Add notes AND acknowledge the overdue test with UTC timestamp
    const updatedTestResult = await this.testResultModel.findOneAndUpdate(
      { _id: id },
      {
        $set: {
          notes: updateData.notes,
          notesSaved: true,
          acknowledged: true,
          acknowledgedBy: user._id,
          acknowledgedAt: new Date(), // MongoDB stores dates in UTC by default
        },
      },
      { new: true },
    );

    if (!updatedTestResult) {
      throw new NotFoundException('Test result not found');
    }
  }

  /**
   * Acknowledge red flag with notes (atomic operation)
   * Implements functional requirements 6.4.3.6 for red flag acknowledgment
   */
  public async acknowledgeRedFlag(id: string, user: any, updateData: AcknowledgeRedFlagDto): Promise<void> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException(MESSAGES.INVALID_TEST_RESULT_ID);
    }

    const testResult = await this.testResultModel.findById(id);

    if (!testResult) {
      throw new NotFoundException('Test result not found');
    }

    if (!testResult.redFlag) {
      throw new BadRequestException('Test result does not have a red flag');
    }

    // Check if any red flag has already been acknowledged (immutable once saved)
    if (testResult?.notesSaved) {
      throw new BadRequestException('Red flag notes cannot be edited once saved');
    }

    // Atomic operation: Add notes AND acknowledge the overdue test with UTC timestamp
    const updatedTestResult = await this.testResultModel.findOneAndUpdate(
      { _id: id },
      {
        $set: {
          notes: updateData.notes,
          notesSaved: true,
          acknowledged: true,
          acknowledgedBy: user._id,
          acknowledgedAt: new Date(), // MongoDB stores dates in UTC by default
        },
      },
      { new: true },
    );

    if (!updatedTestResult) {
      throw new NotFoundException('Test result not found');
    }
  }

  /**
   * Get patient details with historical test results
   * Implements functional requirements 6.4.3.6 for patient details view
   */
  public async getPatientDetails(
    patientId: string,
    query: GetPatientDetailsDto,
  ): Promise<PatientDetailsWithPagination> {
    if (!Types.ObjectId.isValid(patientId)) {
      throw new BadRequestException('Invalid patient ID');
    }

    // Verify patient exists
    const patient = await this.userModel.findById(patientId);
    if (!patient) {
      throw new NotFoundException('Patient not found');
    }

    const { orderBy, orderType, paginationParam } = this.buildPatientDetailsQueryParams(query);
    const sortCondition = this.buildPatientDetailsSortCondition(orderBy, orderType);

    return await this.buildGetPatientDetailsQuery(
      {
        patient: new Types.ObjectId(patientId),
        status: { $ne: TestResultStatus.NEW },
      },
      sortCondition,
      paginationParam,
    );
  }

  /**
   * Get overdue tests specifically for the overdue tests table
   */
  public async getOverdueTests(query: GetOverdueTestsDto): Promise<OverdueTestsWithPagination> {
    const { orderBy, orderType, paginationParam } = this.buildOverdueQueryParams(query);
    const sortCondition = this.buildOverdueTestsSortCondition(orderBy, orderType);

    return await this.buildGetOverdueTestQuery(
      {
        status: TestResultStatus.OVERDUE,
      },
      sortCondition,
      paginationParam,
    );
  }

  /**
   * Build query parameters specific to overdue tests
   */
  private buildOverdueQueryParams(query: GetOverdueTestsDto) {
    const orderBy = this.mapOverdueOrderBy(query.orderBy || 'dueDate');
    const orderType = query.orderType === 'asc' ? 1 : -1;
    const paginationParam = PaginationHelper.buildPaginationQueryParams(query.page || 1, query.limit || 10);

    return { orderBy, orderType, paginationParam };
  }

  /**
   * Map frontend order fields to database fields
   */
  private mapOverdueOrderBy(orderBy: string): string {
    const fieldMapping = {
      username: 'patient.username',
      testName: 'type', // Sort by test type for test name
      dueDate: 'dueDate',
    };

    return fieldMapping[orderBy] || 'dueDate';
  }

  /**
   * Build sort condition for overdue tests
   */
  private buildOverdueTestsSortCondition(orderBy: string, orderType: number): any {
    if (orderBy === 'patient.username') {
      return {
        [orderBy]: orderType,
        type: 1, // Secondary sort by test type
        dueDate: -1, // Tertiary sort by due date (newest first)
      };
    } else if (orderBy === 'type') {
      // testName sorting
      return {
        type: orderType,
        'patient.username': 1, // Secondary sort by username
        dueDate: -1, // Tertiary sort by due date
      };
    } else {
      return {
        [orderBy]: orderType,
        'patient.username': 1, // Secondary sort by username
      };
    }
  }

  /**
   * Build query parameters for patient details
   */
  private buildPatientDetailsQueryParams(query: GetPatientDetailsDto) {
    const orderBy = this.mapPatientDetailsOrderBy(query.orderBy || 'completedDate');
    const orderType = query.orderType === 'asc' ? 1 : -1;
    const paginationParam = PaginationHelper.buildPaginationQueryParams(query.page || 1, query.limit || 10);

    return { orderBy, orderType, paginationParam };
  }

  /**
   * Map frontend order fields to database fields for patient details
   */
  private mapPatientDetailsOrderBy(orderBy: string): string {
    const fieldMapping = {
      completedDate: 'completedDate',
      startDate: 'startDate',
      type: 'type',
    };

    return fieldMapping[orderBy] || 'completedDate';
  }

  /**
   * Build sort condition for patient details
   */
  private buildPatientDetailsSortCondition(orderBy: string, orderType: number): any {
    if (orderBy === 'type') {
      return {
        type: orderType,
        completedDate: -1, // Secondary sort by completion date (newest first)
      };
    } else {
      return {
        [orderBy]: orderType,
        type: 1, // Secondary sort by test type
      };
    }
  }

  /**
   * Build the overdue test query with enhanced population
   */
  private async buildGetOverdueTestQuery(queryCondition, sortCondition, paginationParam) {
    const aggregateQuery = [
      { $match: queryCondition },
      {
        $lookup: {
          from: 'users',
          localField: 'acknowledgedBy',
          foreignField: '_id',
          as: 'acknowledgedBy',
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'patient',
          foreignField: '_id',
          as: 'patient',
        },
      },
      { $unwind: '$patient' },
      {
        $match: {
          'patient.isArchived': { $ne: true },
        },
      },
      { $sort: sortCondition },
      {
        $facet: {
          metadata: [{ $count: 'total' }, { $addFields: { page: paginationParam.page } }],
          data: [{ $skip: paginationParam.skip }, { $limit: paginationParam.limit }],
        },
      },
    ];

    const queryResult = await this.testResultModel.aggregate(aggregateQuery);
    const totalDocs = this.extractTotalDocs(queryResult);
    const paginationRO = PaginationHelper.buildPaginationRO(paginationParam.page, paginationParam.limit, totalDocs);

    return TestResultService.buildOverdueTestsWithPagination(queryResult[0] || [], paginationRO);
  }

  /**
   * Build the patient details query with enhanced population and RAG breakdown
   */
  private async buildGetPatientDetailsQuery(queryCondition, sortCondition, paginationParam) {
    const aggregateQuery = [
      { $match: queryCondition },
      {
        $lookup: {
          from: 'users',
          localField: 'acknowledgedBy',
          foreignField: '_id',
          as: 'acknowledgedBy',
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'patient',
          foreignField: '_id',
          as: 'patient',
        },
      },
      { $unwind: '$patient' },
      {
        $addFields: {
          // Compute subtype based on test type and patient IBD subtype
          computedSubtype: {
            $cond: {
              if: { $eq: ['$type', TestType.SYMPTOMS] },
              then: {
                $cond: {
                  if: { $eq: ['$patient.ibdSubtype', IBDSubtype.CROHNS_DISEASE] },
                  then: SymptomsTestSubtype.HBI,
                  else: {
                    $cond: {
                      if: { $eq: ['$patient.ibdSubtype', IBDSubtype.ULCERATIVE_COLITIS] },
                      then: SymptomsTestSubtype.SCCAI,
                      else: SymptomsTestSubtype.HBI, // Default fallback
                    },
                  },
                },
              },
              else: {
                $cond: {
                  if: { $eq: ['$type', TestType.MENTAL_WELLBEING] },
                  then: 'MENTAL_WELLBEING',
                  else: 'GENERAL_WELLBEING',
                },
              },
            },
          },
          // Compute completed status (true if status is COMPLETED)
          computedCompleted: { $eq: ['$status', TestResultStatus.COMPLETED] },
          // Compute overdue status (true if status is OVERDUE)
          computedOverdue: { $eq: ['$status', TestResultStatus.OVERDUE] },
        },
      },
      {
        $match: {
          'patient.isArchived': { $ne: true },
        },
      },
      { $sort: sortCondition },
      {
        $facet: {
          metadata: [{ $count: 'total' }, { $addFields: { page: paginationParam.page } }],
          data: [{ $skip: paginationParam.skip }, { $limit: paginationParam.limit }],
        },
      },
    ];

    const queryResult = await this.testResultModel.aggregate(aggregateQuery);
    const totalDocs = this.extractTotalDocs(queryResult);
    const paginationRO = PaginationHelper.buildPaginationRO(paginationParam.page, paginationParam.limit, totalDocs);

    return TestResultService.buildPatientDetailsWithPagination(queryResult[0] || [], paginationRO);
  }

  /**
   * Build the response object for patient details
   */
  private static buildPatientDetailsWithPagination(
    queryResult,
    paginationRO: PaginationRO,
  ): PatientDetailsWithPagination {
    const data = queryResult.data || [];

    const items = data.map((testResult) => this.processPatientTestResultItem(testResult));

    return {
      data: items,
      metadata: {
        total: paginationRO.totalDocs,
        page: paginationRO.page,
        limit: paginationRO.limit,
        totalPages: paginationRO.totalPages,
      },
    };
  }

  /**
   * Process individual patient test result item with RAG breakdown
   */
  private static processPatientTestResultItem(testResult): PatientTestResultRO {
    const patient = testResult.patient;
    const acknowledgedBy = testResult.acknowledgedBy?.[0];

    return {
      id: testResult._id,
      title: this.buildTestResultTitle(testResult),
      patient: {
        id: patient._id,
        username: patient.username,
        firstName: patient.firstName,
        lastName: patient.lastName,
        fullName: `${patient.firstName} ${patient.lastName}`.trim(),
        ibdSubtype: patient.ibdSubtype,
      },
      acknowledgedBy: acknowledgedBy
        ? {
            id: acknowledgedBy._id,
            firstName: acknowledgedBy.firstName,
            lastName: acknowledgedBy.lastName,
            fullName: `${acknowledgedBy.firstName} ${acknowledgedBy.lastName}`.trim(),
          }
        : null,
      reds: testResult.reds || 0,
      greens: testResult.greens || 0,
      ambers: testResult.ambers || 0,
      totalScores: String(NumberHelper.formatScore(testResult.totalScores) || '0'),
      ttc: testResult.ttc,
      anxietyScore: testResult.anxietyScore,
      depressionScore: testResult.depressionScore,
      sibdqScore: testResult.sibdqScore,
      eqScore: testResult.eqScore,
      notes: testResult.notes,
      redFlag: testResult.redFlag || false,
      acknowledged: testResult.acknowledged || false,
      status: testResult.status,
      startDate: Date.parse(testResult.startDate) / 1000,
      completedDate: Date.parse(testResult.completedDate) / 1000,
      dueDate: Date.parse(testResult.dueDate) / 1000,
      flagDetails: testResult.flagDetails?.map((flag) => {
        return {
          flagType: flag.flagType,
          flagReason: flag.flagReason,
          flaggedAt: flag.flaggedAt?.toISOString(),
          questionNumbers: flag.questionNumbers,
          testSubtype: flag.testSubtype,
        };
      }),
      ragBreakdown: this.buildRAGBreakdown(testResult),
    };
  }

  /**
   * Build RAG breakdown for hover-over functionality
   */
  private static buildRAGBreakdown(testResult): RAGBreakdownRO | undefined {
    if (testResult.type !== TestType.SYMPTOMS || !testResult.flagDetails) {
      return undefined;
    }

    const patient = testResult.patient;
    const ibdSubtype = patient?.ibdSubtype;

    if (!ibdSubtype) {
      return undefined;
    }

    // Find red/amber flag details with question numbers
    const redAmberFlags = testResult.flagDetails.filter(
      (flag) => flag.flagType === 'RED_AMBER_RESULT' && flag.questionNumbers,
    );

    if (redAmberFlags.length === 0) {
      return undefined;
    }

    const redQuestions: QuestionBreakdownRO[] = [];
    const amberQuestions: QuestionBreakdownRO[] = [];
    const greenQuestions: QuestionBreakdownRO[] = [];

    // Process question numbers from flag details
    redAmberFlags.forEach((flag) => {
      flag.questionNumbers?.forEach((questionNumber) => {
        const questionBreakdown: QuestionBreakdownRO = {
          questionNumber,
          ibdSubtype: ibdSubtype === IBDSubtype.CROHNS_DISEASE ? "Crohn's" : 'Colitis',
          displayText: `${ibdSubtype === IBDSubtype.CROHNS_DISEASE ? "Crohn's" : 'Colitis'} - ${questionNumber}`,
        };

        // Determine if this is red, amber, or green based on the test result
        // This is a simplified approach - in a real implementation, you'd need to
        // analyze the actual answer data to determine the RAG classification
        if (testResult.reds > 0) {
          redQuestions.push(questionBreakdown);
        } else if (testResult.ambers > 0) {
          amberQuestions.push(questionBreakdown);
        } else {
          greenQuestions.push(questionBreakdown);
        }
      });
    });

    return {
      redQuestions,
      amberQuestions,
      greenQuestions,
    };
  }

  /**
   * Build the response object for overdue tests
   */
  private static buildOverdueTestsWithPagination(queryResult, paginationRO: PaginationRO): OverdueTestsWithPagination {
    const data = queryResult.data || [];

    const items = data.map((testResult) => this.processOverdueTestResultItem(testResult));

    return {
      items,
      totalDocs: paginationRO.totalDocs,
      limit: paginationRO.limit,
      totalPages: paginationRO.totalPages,
      page: paginationRO.page,
      pagingCounter: (paginationRO.page - 1) * paginationRO.limit + 1,
      hasPrevPage: paginationRO.page > 1,
      hasNextPage: paginationRO.page < paginationRO.totalPages,
      prevPage: paginationRO.page > 1 ? paginationRO.page - 1 : undefined,
      nextPage: paginationRO.page < paginationRO.totalPages ? paginationRO.page + 1 : undefined,
    };
  }

  /**
   * Process individual overdue test result item
   */
  private static processOverdueTestResultItem(testResult): OverdueTestResultRO {
    const patient = testResult.patient;
    const acknowledgedBy = testResult.acknowledgedBy?.[0];

    return {
      id: testResult._id,
      patientUsername: patient.username,
      testName: this.formatOverdueTestName(testResult),
      dueDate: this.formatDueDate(testResult.dueDate),
      acknowledged: testResult.acknowledged || false,
      acknowledgedBy: acknowledgedBy
        ? {
            id: acknowledgedBy._id,
            firstName: acknowledgedBy.firstName,
            lastName: acknowledgedBy.lastName,
            fullName: `${acknowledgedBy.firstName} ${acknowledgedBy.lastName}`.trim(),
          }
        : null,
      acknowledgedAt: testResult.acknowledgedAt ? moment(testResult.acknowledgedAt).format('DD/MM/YY HH:mm') : null,
      notes: testResult.notes || '',
      canEditNotes: !testResult.notesSaved, // Can only edit if notes haven't been saved before
      patient: {
        id: patient._id,
        username: patient.username,
        firstName: patient.firstName,
        lastName: patient.lastName,
      },
      testType: testResult.type,
      status: testResult.status,
    };
  }

  /**
   * Format test name for display
   */
  private static formatTestName(testType: number): string {
    const testNames = {
      [TestType.SYMPTOMS]: 'Test 1: Symptoms',
      [TestType.MENTAL_WELLBEING]: 'Test 2: Mental Wellbeing',
      [TestType.GENERAL_WELLBEING]: 'Test 3: General Wellbeing',
    };

    return testNames[testType] || `Test ${testType}`;
  }

  /**
   * Format overdue test name with exact days overdue as per requirements 6.4.3.4
   */
  private static formatOverdueTestName(testResult): string {
    try {
      const daysOverdue = this.calculateExactDaysOverdue(testResult);

      if (testResult.type === TestType.SYMPTOMS) {
        // Get IBD subtype from patient data
        const ibdSubtype = testResult.patient?.ibdSubtype;
        if (ibdSubtype === IBDSubtype.CROHNS_DISEASE) {
          return `Test 1 - HBI Symptoms - ${daysOverdue} days`;
        } else if (ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS) {
          return `Test 1 - SCCAI Symptoms - ${daysOverdue} days`;
        } else {
          return `Test 1 - Symptoms - ${daysOverdue} days`;
        }
      } else if (testResult.type === TestType.MENTAL_WELLBEING) {
        return `Test 2 - Mental Wellbeing - ${daysOverdue} days`;
      } else if (testResult.type === TestType.GENERAL_WELLBEING) {
        return `Test 3 - General Wellbeing - ${daysOverdue} days`;
      }

      return `Test ${testResult.type} - ${daysOverdue} days`;
    } catch (error) {
      // Error handling as specified in requirements
      return this.formatTestNameWithError(testResult.type);
    }
  }

  /**
   * Calculate exact days overdue from last completed test
   */
  private static calculateExactDaysOverdue(testResult): number {
    if (!testResult.previousCompletedDate) {
      throw new Error('No previous completed date available');
    }

    const daysSinceLastTest = moment().utc().diff(moment(testResult.previousCompletedDate), 'days');
    return daysSinceLastTest;
  }

  /**
   * Format test name with error message when days calculation fails
   */
  private static formatTestNameWithError(testType: number): string {
    const baseNames = {
      [TestType.SYMPTOMS]: 'Test 1 - Symptoms',
      [TestType.MENTAL_WELLBEING]: 'Test 2 - Mental Wellbeing',
      [TestType.GENERAL_WELLBEING]: 'Test 3 - General Wellbeing',
    };

    const baseName = baseNames[testType] || `Test ${testType}`;
    return `${baseName} - We couldn't determine how overdue this test is. Please check manually.`;
  }

  /**
   * Format due date in DD/MM/YY format
   */
  private static formatDueDate(dueDate: Date): string {
    return moment(dueDate).format('DD/MM/YY');
  }

  public async submitTest(id, user, updateData) {
    if (user.role === UserRole.DEMO) {
      return this.getDemoTestResult(id, user, updateData);
    }

    const testResult = await this.findAndValidateTestResult(id);

    if (!updateData?.cancelled) {
      await this.validateTestAnswers(testResult, updateData);
    }

    this.setTestStartDate(testResult);

    return await this.processTestByType(testResult, user, updateData);
  }

  private async findAndValidateTestResult(id: string): Promise<any> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException(`Test result is not found.`);
    }

    const testResult = await this.testResultModel
      .findOne({
        _id: id,
        status: {
          $nin: [TestResultStatus.COMPLETED, TestResultStatus.CANCELLED],
        },
      })
      .populate({
        path: 'test',
        populate: {
          path: 'questions',
          model: 'Question',
          populate: [
            {
              path: 'questions',
              model: 'Question',
            },
            {
              path: 'answers.questions',
              model: 'Question',
            },
          ],
        },
      });

    if (!testResult) {
      throw new NotFoundException(`Test result is not found.`);
    }

    // Ensure detail is populated with the full test data including populated questions
    if (testResult.test && testResult.test.questions) {
      testResult.detail = testResult.test;
    }

    return testResult;
  }

  private setTestStartDate(testResult: any): void {
    if (testResult.startDate === undefined) {
      testResult.startDate = new Date();
    }
  }

  private async processTestByType(testResult: any, user: any, updateData: any): Promise<any> {
    const testProcessors = {
      [TestType.SYMPTOMS]: () => this.calculatorService.calculateSymptomsTest(testResult, user, updateData),
      [TestType.MENTAL_WELLBEING]: () =>
        this.calculatorService.calculateMentalWellbeingTest(testResult, user, updateData),
      [TestType.GENERAL_WELLBEING]: () =>
        this.calculatorService.calculateGeneralWellbeingTest(testResult, user, updateData),
    };

    const processor = testProcessors[testResult.type];
    if (!processor) {
      throw new BadRequestException('Invalid test type');
    }

    return await processor();
  }

  private async buildGetTestResultQuery(queryCondition, sortCondition, paginationParam) {
    const pipeline = [
      { $match: queryCondition },
      {
        $lookup: {
          from: 'users',
          localField: 'acknowledgedBy',
          foreignField: '_id',
          as: 'acknowledgedBy',
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'patient',
          foreignField: '_id',
          as: 'patient',
        },
      },
      { $unwind: '$patient' },
      {
        $addFields: {
          // Compute subtype based on test type and patient IBD subtype
          computedSubtype: {
            $cond: {
              if: { $eq: ['$type', TestType.SYMPTOMS] },
              then: {
                $cond: {
                  if: { $eq: ['$patient.ibdSubtype', IBDSubtype.CROHNS_DISEASE] },
                  then: SymptomsTestSubtype.HBI,
                  else: {
                    $cond: {
                      if: { $eq: ['$patient.ibdSubtype', IBDSubtype.ULCERATIVE_COLITIS] },
                      then: SymptomsTestSubtype.SCCAI,
                      else: SymptomsTestSubtype.HBI, // Default fallback
                    },
                  },
                },
              },
              else: {
                $cond: {
                  if: { $eq: ['$type', TestType.MENTAL_WELLBEING] },
                  then: 'MENTAL_WELLBEING',
                  else: 'GENERAL_WELLBEING',
                },
              },
            },
          },
          // Compute completed status (true if status is COMPLETED)
          computedCompleted: { $eq: ['$status', TestResultStatus.COMPLETED] },
          // Compute overdue status (true if status is OVERDUE)
          computedOverdue: { $eq: ['$status', TestResultStatus.OVERDUE] },
        },
      },
      { $sort: sortCondition },
      {
        $facet: {
          metadata: [{ $count: 'total' }, { $addFields: { page: paginationParam.page } }],
          data: [{ $skip: paginationParam.skip }, { $limit: paginationParam.limit }],
        },
      },
    ];
    const queryResult = await this.testResultModel.aggregate(pipeline);

    const totalDocs = this.extractTotalDocs(queryResult);
    const paginationRO = PaginationHelper.buildPaginationRO(paginationParam.page, paginationParam.limit, totalDocs);

    return TestResultService.buildTestResultsWithPagination(queryResult[0] || [], paginationRO);
  }

  private extractTotalDocs(queryResult: any[]): number {
    if (queryResult[0].metadata !== undefined && queryResult[0].metadata[0] !== undefined) {
      return queryResult[0].metadata[0].total;
    }
    return 0;
  }

  private static buildTestResultsWithPagination(queryResult, paginationRO: PaginationRO): TestResultsWithPagination {
    const result = {
      items: [],
      totalDocs: paginationRO.totalDocs,
      limit: paginationRO.limit,
      totalPages: paginationRO.totalPages,
      page: paginationRO.page,
    };

    queryResult.data.forEach((testResult) => {
      const processedItem = this.processTestResultItem(testResult);
      result.items.push(processedItem);
    });

    return result;
  }

  private static processTestResultItem(testResult): any {
    const title = this.buildTestResultTitle(testResult);
    const ragCounts = this.categorizeReportItems(testResult.report);

    return {
      id: testResult._id,
      title,
      patient: this.buildPatientInfo(testResult.patient),
      acknowledgedBy: this.buildAcknowledgedByInfo(testResult.acknowledgedBy),
      reds: testResult.status === TestResultStatus.CANCELLED ? [] : ragCounts.reds,
      greens: testResult.status === TestResultStatus.CANCELLED ? [] : ragCounts.greens,
      ambers: testResult.status === TestResultStatus.CANCELLED ? [] : ragCounts.ambers,
      totalScores: NumberHelper.formatScore(testResult.totalScores),
      ttc: testResult.ttc,
      anxietyScore: testResult.anxietyScore,
      depressionScore: testResult.depressionScore,
      sibdqScore: testResult.sibdqScore,
      eqScore: testResult.eqScore,
      notes: testResult.notes,
      redFlag: testResult.redFlag,
      acknowledged: testResult.acknowledged,
      status: testResult.status,
      startDate: Date.parse(testResult.startDate) / 1000,
      completedDate: Date.parse(testResult.completedDate) / 1000,
      dueDate: Date.parse(testResult.dueDate) / 1000,
      flagDetails: testResult?.flagDetails
    };
  }

  private static buildTestResultTitle(testResult): string {
    let title = this.getBaseTestTitle(testResult.type, testResult.title);

    if (testResult.redFlag) {
      if (testResult.type === TestType.SYMPTOMS && testResult.patient?.ibdSubtype) {
        const ibdPrefix =
          testResult.patient.ibdSubtype === IBDSubtype.CROHNS_DISEASE
            ? SymptomsTestSubtype.HBI
            : testResult.patient.ibdSubtype === IBDSubtype.ULCERATIVE_COLITIS
              ? SymptomsTestSubtype.SCCAI
              : '';
        title = `Test ${TestType.SYMPTOMS} - ${ibdPrefix} Symptoms`;
      }

      if (testResult.type === TestType.MENTAL_WELLBEING) {
        const anxietyScore = testResult.anxietyScore || 0;
        const depressionScore = testResult.depressionScore || 0;

        if (anxietyScore > 11 && depressionScore > 11) {
          title = TestTitle.MENTAL_WELLBEING_ANXIETY_DEPRESSION;
        } else if (anxietyScore > 11) {
          title = TestTitle.MENTAL_WELLBEING_ANXIETY;
        } else if (depressionScore > 11) {
          title = TestTitle.MENTAL_WELLBEING_DEPRESSION;
        }
      }

      if (testResult.report) {
        const flaggedQuestions = testResult.report
          .filter((item) => item.redFlag)
          .map((item) => `Q${item.order}`)
          .join(', ');

        if (flaggedQuestions) {
          title = `${title} - ${flaggedQuestions}`;
        }
      }
    }

    if (testResult.status === TestResultStatus.OVERDUE) {
      const overdueText = this.handleDueDateText(testResult);
      return title + overdueText;
    }

    return title;
  }

  private static getBaseTestTitle(type: TestType, title: string): string {
    const testPrefixes = {
      [TestType.SYMPTOMS]: 'Test 1: ',
      [TestType.MENTAL_WELLBEING]: 'Test 2: ',
      [TestType.GENERAL_WELLBEING]: 'Test 3: ',
    };

    const prefix = testPrefixes[type] || 'Test: ';
    return prefix + title;
  }

  private static categorizeReportItems(report: any[]): { reds: string[]; ambers: string[]; greens: string[] } {
    const reds = [];
    const ambers = [];
    const greens = [];

    report.forEach((reportItem) => {
      const itemText = reportItem.code + ' - ' + reportItem.question;

      if (reportItem.rag === RAGType.A) {
        ambers.push(itemText);
      } else if (reportItem.rag === RAGType.G) {
        greens.push(itemText);
      } else if (reportItem.rag === RAGType.R) {
        reds.push(itemText);
      }
    });

    return { reds, ambers, greens };
  }

  private static buildPatientInfo(patient: any): any {
    return patient
      ? {
          patientId: patient._id,
          username: patient.username,
          ibdSubtype: patient.ibdSubtype,
        }
      : null;
  }

  private static buildAcknowledgedByInfo(acknowledgedBy: any[]): any {
    return acknowledgedBy[0]
      ? {
          clinicianId: acknowledgedBy[0]._id,
          username: acknowledgedBy[0].username,
          fullName:
            acknowledgedBy[0]?.fullName ||
            `${acknowledgedBy[0]?.firstName ?? ''} ${acknowledgedBy[0]?.lastName ?? ''}`.trim(),
        }
      : null;
  }

  public static handleDueDateText(testResult) {
    try {
      const daysOverdue = this.calculateExactDaysOverdue(testResult);
      return ` - ${daysOverdue} days`;
    } catch (error) {
      // Fallback to original calculation if exact calculation fails
      const overDueCount = testResult.overDueCount ? testResult.overDueCount : 0;
      return ` - ${this.getOverdueDateByType(testResult.type) + OVERDUE_INTERVAL_DAYS * overDueCount} days`;
    }
  }

  public static getOverdueDateByType(type): number {
    const overdueDays = {
      [TestType.SYMPTOMS]: OverdueThreshold.SYMPTOMS_OVERDUE_DAYS,
      [TestType.MENTAL_WELLBEING]: OverdueThreshold.MENTAL_WELLBEING_OVERDUE_DAYS,
      [TestType.GENERAL_WELLBEING]: OverdueThreshold.GENERAL_WELLBEING_OVERDUE_DAYS,
    };

    return overdueDays[type] || OverdueThreshold.GENERAL_WELLBEING_OVERDUE_DAYS;
  }

  async findOverdueTestByType() {
    const now = moment().utc();
    now.set({ hour: 0, minute: 0, second: 0, millisecond: 0 });

    this.logger.log('Finding overdue tests - looking for tests with NEW status that could be overdue');

    // Find all NEW tests that could potentially be overdue
    // We'll check the actual overdue status in shouldSkipOverdueProcessing
    const testResults = await this.testResultModel
      .find({
        status: TestResultStatus.NEW,
        $or: [
          // Tests with dueDate in the past (traditional logic)
          { dueDate: { $lte: now.format() } },
          // Tests with previousCompletedDate that might be overdue
          { previousCompletedDate: { $exists: true, $ne: null } },
        ],
      })
      .populate('patient');

    this.logger.log(`Found ${testResults.length} potential overdue tests to process`);

    // Log details of each test found
    testResults.forEach((test, index) => {
      this.logger.log(`Potential overdue test ${index + 1}:
        - ID: ${test._id}
        - Patient: ${test.patient?._id}
        - Type: ${test.type}
        - Status: ${test.status}
        - Due Date: ${test.dueDate}
        - Previous Completed Date: ${test.previousCompletedDate}`);
    });

    return testResults;
  }

  /**
   * Send due date notifications for tests due today at 9 AM
   * Called by cron job as per functional requirements 6.3.7.1
   */
  async sendDueDateNotifications(): Promise<void> {
    this.logger.log('Starting due date notification process at 9 AM');

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Start of today

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1); // Start of tomorrow

    // Find all tests due today
    const testsDueToday = await this.testResultModel
      .find({
        status: TestResultStatus.NEW,
        dueDate: {
          $gte: today,
          $lt: tomorrow,
        },
      })
      .populate('patient');

    this.logger.log(`Found ${testsDueToday.length} tests due today for notification`);

    // Send notifications for each test due today
    for (const testResult of testsDueToday) {
      if (!testResult.patient) {
        this.logger.warn(`No patient found for test result ${testResult._id}`);
        continue;
      }

      try {
        await this.notificationSchedulerService.sendTestDueNotification(
          testResult.patient,
          testResult.type,
          testResult.title,
        );
      } catch (error) {
        this.logger.error(
          `Failed to send notification for test ${testResult._id}, patient ${testResult.patient._id}: ${error.message}`,
        );
      }
    }

    this.logger.log('Completed due date notification process');
  }

  async triggerOverdueFlag(overDueTestResults) {
    this.logger.log('Start Trigger overdue flag');

    for (const testResult of overDueTestResults) {
      if (!testResult?.patient) {
        continue;
      }

      const shouldSkip = await this.shouldSkipOverdueProcessing(testResult)
      if (shouldSkip) {
        continue;
      }

      await this.processOverdueTestResult(testResult);
    }

    this.logger.log('Completed Trigger overdue flag');
  }

  private async shouldSkipOverdueProcessing(testResult: any): Promise<boolean> {
    const isUserTriggerOverdueToday = await this.isUserTriggerOverdueToday(testResult);
    const patientArchived = testResult.patient.isArchived;
    const isActuallyOverdue = this.isTestActuallyOverdue(testResult);

    this.logger.log(`Overdue processing check for test ${testResult._id}:
      - Patient archived: ${patientArchived}
      - Already triggered today: ${isUserTriggerOverdueToday}
      - Actually overdue: ${isActuallyOverdue}
      - Previous completed date: ${testResult.previousCompletedDate}
      - Due date: ${testResult.dueDate}`);

    // Skip if: patient is archived, already triggered today, or NOT actually overdue
    return patientArchived || isUserTriggerOverdueToday || !isActuallyOverdue;
  }

  /**
   * Check if a test is actually overdue based on functional requirements:
   * - Use previousCompletedDate as reference point if available
   * - Fall back to dueDate for first-time tests
   * - Apply appropriate threshold based on test type
   */
  private isTestActuallyOverdue(testResult: any): boolean {
    const now = moment().utc();
    const overdueThreshold = TestResultService.getOverdueDateByType(testResult.type);

    let referenceDate: moment.Moment;

    if (testResult.previousCompletedDate) {
      // Use last completed test date as reference (primary logic)
      referenceDate = moment(testResult.previousCompletedDate).utc();
      this.logger.log(`Using previousCompletedDate as reference: ${referenceDate.format()}`);
    } else {
      // Fall back to dueDate for first-time tests
      referenceDate = moment(testResult.dueDate).utc();
      this.logger.log(`Using dueDate as reference (no previous completed): ${referenceDate.format()}`);
    }

    const daysSinceReference = now.diff(referenceDate, 'days');
    const isOverdue = daysSinceReference > overdueThreshold;

    this.logger.log(`Test ${testResult._id} overdue calculation:
      - Reference date: ${referenceDate.format()}
      - Days since reference: ${daysSinceReference}
      - Overdue threshold: ${overdueThreshold}
      - Is overdue: ${isOverdue}`);

    return isOverdue;
  }

  private async processOverdueTestResult(testResult: any): Promise<void> {
    const overDueCount = testResult.overDueCount || 0;

    if (!overDueCount) {
      await this.createFirstTimeOverdueTest(testResult);
    } else {
      await this.processRecurringOverdueTest(testResult, overDueCount);
    }
  }

  private async createFirstTimeOverdueTest(testResult: any): Promise<void> {
    testResult.status = TestResultStatus.OVERDUE;
    testResult.startDate = moment.utc();
    testResult.overDueTriggered = true;
    testResult.overDueCount = 0;
    testResult.redFlag = true;

    // Calculate days since last test and log overdue flag
    const overDueStandard = TestResultService.getOverdueDateByType(testResult.type);
    const daysSinceLastTest = testResult.previousCompletedDate
      ? moment().utc().diff(moment(testResult.previousCompletedDate), 'days')
      : overDueStandard;

    const testTypeName = this.getTestTypeName(testResult.type);

    if (testResult.patient?.username) {
      await this.flagLoggingService.logOverdueFlag(
        testResult,
        testResult.patient.username,
        daysSinceLastTest,
        testTypeName,
      );
    }

    this.logger.log('Create Overdue test first time');
    await this.testService.createTestByOverdue(testResult);
    await testResult.save();
  }

  private async processRecurringOverdueTest(testResult: any, overDueCount: number): Promise<void> {
    const overDueStandard = TestResultService.getOverdueDateByType(testResult.type);
    const previousCompletedDate = moment(testResult.previousCompletedDate)
      .utc()
      .add(OVERDUE_INTERVAL_DAYS * overDueCount + overDueStandard, 'days');
    const overdueDuration = moment().utc().diff(previousCompletedDate, 'days');

    if (overdueDuration > 0) {
      this.logger.log(`Create Overdue test overdue count ${testResult.overDueCount}`);
      testResult.status = TestResultStatus.OVERDUE;
      testResult.startDate = moment.utc();
      testResult.overDueTriggered = true;
      testResult.redFlag = true;

      // Log recurring overdue flag
      const totalDaysSinceLastTest = moment().utc().diff(moment(testResult.previousCompletedDate), 'days');
      const testTypeName = this.getTestTypeName(testResult.type);

      if (testResult.patient?.username) {
        await this.flagLoggingService.logOverdueFlag(
          testResult,
          testResult.patient.username,
          totalDaysSinceLastTest,
          testTypeName,
        );
      }

      await testResult.save();
      await this.testService.createTestByOverdue(testResult);
    }
  }

  async isCompletedTestExisted(testResult: any): Promise<boolean> {
    return !!(await this.testResultModel.exists({
      patient: testResult.patient,
      type: testResult.type,
      status: TestResultStatus.COMPLETED,
    }));
  }

  async isUserTriggerOverdueToday(testResult): Promise<boolean> {
    const now = moment().utc();
    now.set({ hour: 0, minute: 0, second: 0, millisecond: 0 });

    // Check if we already processed THIS SPECIFIC overdue test today
    // by looking for tests created today with the same overDueCount
    const currentOverDueCount = testResult.overDueCount || 0;

    const existingTodayTest = await this.testResultModel.exists({
      patient: testResult.patient,
      type: testResult.type,
      status: TestResultStatus.NEW,
      overDueCount: currentOverDueCount + 1, // The next overDueCount that would be created
      createdAt: { $gt: now },
    });

    this.logger.log(`Checking if overdue already triggered today for test ${testResult._id}:
      - Current overDueCount: ${currentOverDueCount}
      - Looking for tests with overDueCount: ${currentOverDueCount + 1}
      - Found existing test created today: ${!!existingTodayTest}`);

    return !!existingTodayTest;
  }

  private async validateTestAnswers(testResult: any, updateData: any): Promise<void> {
    const testDetail = testResult.detail;

    if (!testDetail || !testDetail.questions) {
      throw new BadRequestException(MESSAGES.QUESTIONS_FAILED_TO_LOAD);
    }

    await this.validateSymptomsTestAnswers(testDetail.questions, updateData);
  }

  private async validateSymptomsTestAnswers(questions: any[], updateData: any): Promise<void> {
    if (!Array.isArray(questions) || questions.length === 0) {
      return;
    }

    const requestQuestions = this.getAllQuestionsFromRequest(updateData.detail.questions);

    for (const question of questions) {
      await this.validateQuestionWithContext(question, updateData, requestQuestions);
    }
  }

  private async validateQuestionWithContext(question: any, updateData: any, requestQuestions: any[]): Promise<void> {
    const existsInRequest = requestQuestions.some((rq) => rq.code === question.code);

    if (existsInRequest) {
      await this.validateSingleQuestion(question, updateData);
    }

    await this.validateNestedQuestions(question, updateData);
  }

  private async validateNestedQuestions(question: any, updateData: any): Promise<void> {
    // Validate nested questions if they exist
    if (question.questions && question.questions.length > 0) {
      await this.validateSymptomsTestAnswers(question.questions, updateData);
    }

    // Validate follow-up questions in answers
    if (question.answers) {
      for (const answer of question.answers) {
        if (answer.questions && answer.questions.length > 0) {
          await this.validateSymptomsTestAnswers(answer.questions, updateData);
        }
      }
    }
  }

  private getAllQuestionsFromRequest(questions: any[]): any[] {
    const allQuestions = [];

    if (!questions) return allQuestions;

    for (const question of questions) {
      allQuestions.push(question);
      allQuestions.push(...this.getNestedQuestionsFromQuestion(question));
    }

    return allQuestions;
  }

  private getNestedQuestionsFromQuestion(question: any): any[] {
    const nestedQuestions = [];

    // Add nested questions
    if (question.questions) {
      nestedQuestions.push(...this.getAllQuestionsFromRequest(question.questions));
    }

    // Add questions in answers
    if (question.answers) {
      for (const answer of question.answers) {
        if (answer.questions) {
          nestedQuestions.push(...this.getAllQuestionsFromRequest(answer.questions));
        }
      }
    }

    return nestedQuestions;
  }

  private async validateSingleQuestion(question: any, updateData: any): Promise<void> {
    if (!question.code) {
      return;
    }

    const context: QuestionValidationContext = {
      question,
      updateData,
      submittedAnswer: this.findAnswerForQuestion(question, updateData),
    };

    if (this.isConditionalQuestionNotApplicable(context)) {
      return;
    }

    this.validateQuestionContext(context);
  }

  private validateQuestionContext(context: QuestionValidationContext): void {
    this.validateMandatoryField(context.question, context.submittedAnswer);
    this.validateNumericInput(context.question, context.submittedAnswer);
    this.validateTextLength(context.question, context.submittedAnswer);
  }

  private validateMandatoryField(question: any, submittedAnswer: any): void {
    const isMandatory = this.isMandatoryQuestion(question);
    const hasValidAnswer = this.hasValidAnswer(submittedAnswer, question);

    if (isMandatory && !hasValidAnswer) {
      throw new BadRequestException(MESSAGES.MANDATORY_FIELD_INCOMPLETE);
    }
  }

  private validateNumericInput(question: any, submittedAnswer: any): void {
    if (question.type === 'NUMERIC_INPUT' && submittedAnswer?.numericValue !== undefined) {
      if (question?.code === NumericQuestionCode.GW_12) {
        if (submittedAnswer?.numericValue < 0 || submittedAnswer?.numericValue > 999) {
          throw new BadRequestException(MESSAGES.NUMERIC_VALUE_TOO_LONG);
        }
      }

      this.validateNumericAnswer(submittedAnswer.numericValue, question);
    }
  }

  private validateTextLength(question: any, submittedAnswer: any): void {
    if (
      (question?.code === TextQuestionCode.GW_1 || question?.code === TextQuestionCode.GW_5) &&
      submittedAnswer?.textValue
    ) {
      if (submittedAnswer?.textValue?.length > 250) {
        throw new BadRequestException(MESSAGES.TEXT_FIELD_TOO_LONG);
      }
    }

    if (submittedAnswer?.textValue && question.answers) {
      for (const answer of question.answers) {
        if (answer?.hasTextField && answer?.maxCharacters) {
          if (submittedAnswer?.textValue?.length > answer?.maxCharacters) {
            throw new BadRequestException(`Text cannot exceed ${answer?.maxCharacters} characters.`);
          }
        }
      }
    }
  }

  private isConditionalQuestionNotApplicable(context: QuestionValidationContext): boolean {
    const parentQuestion = this.findParentQuestionWithNestedQuestions(
      context.question.code,
      context.updateData.detail.questions,
    );

    if (!parentQuestion) {
      return false;
    }

    const parentAnswer = this.findParentAnswerForNestedQuestion(context.question.code, parentQuestion);

    return parentAnswer ? !parentAnswer.selected : false;
  }

  private findParentQuestionWithNestedQuestions(questionCode: string, questions: any[]): any {
    for (const q of questions) {
      const foundInAnswers = this.searchForNestedQuestionInAnswers(q, questionCode);
      if (foundInAnswers) {
        return q;
      }

      const foundInNestedQuestions = this.searchInNestedQuestions(q, questionCode);
      if (foundInNestedQuestions) {
        return foundInNestedQuestions;
      }
    }
    return null;
  }

  private searchForNestedQuestionInAnswers(question: any, questionCode: string): boolean {
    if (!question.answers) return false;

    for (const answer of question.answers) {
      if (answer.questions && this.hasQuestionWithCode(answer.questions, questionCode)) {
        return true;
      }
    }
    return false;
  }

  private searchInNestedQuestions(question: any, questionCode: string): any {
    if (question.questions) {
      return this.findParentQuestionWithNestedQuestions(questionCode, question.questions);
    }
    return null;
  }

  private hasQuestionWithCode(questions: any[], questionCode: string): boolean {
    for (const q of questions) {
      if (q.code === questionCode) {
        return true;
      }
      if (q.questions && this.hasQuestionWithCode(q.questions, questionCode)) {
        return true;
      }
    }
    return false;
  }

  private findParentAnswerForNestedQuestion(questionCode: string, parentQuestion: any): any {
    for (const answer of parentQuestion.answers) {
      if (answer.questions && this.hasQuestionWithCode(answer.questions, questionCode)) {
        return answer;
      }
    }
    return null;
  }

  private isMandatoryQuestion(question: any): boolean {
    // Check if question has explicit mandatory property
    if (question.mandatory !== undefined) {
      return question.mandatory;
    }

    // Default behavior: all questions except checkboxes are mandatory
    return question.type !== 'CHECKBOX_TYPE';
  }

  private hasValidAnswer(answer: any, question: any): boolean {
    if (!answer) {
      return false;
    }

    const validationMethods = {
      MULTIPLE_CHOICE_TYPE: () => answer.selectedAnswerId || answer.hasSelectedAnswer,
      YES_NO: () => answer.selectedAnswerId || answer.hasSelectedAnswer,
      CHECKBOX_TYPE: () => true, // Checkboxes are optional
      NUMERIC_INPUT: () => answer.numericValue !== undefined && answer.numericValue !== null,
    };

    const validator = validationMethods[question.type];
    return validator ? validator() : false;
  }

  private findAnswerForQuestion(question: any, updateData: any): AnswerSearchResult | null {
    if (updateData.detail && updateData.detail.questions) {
      return this.findAnswerInQuestions(question.code, updateData.detail.questions);
    }
    return null;
  }

  private findAnswerInQuestions(questionCode: string, questions: any[]): AnswerSearchResult | null {
    for (const q of questions) {
      if (q.code === questionCode) {
        return this.processQuestionMatch(q);
      }

      const nestedResult = this.searchNestedQuestions(questionCode, q);
      if (nestedResult) {
        return nestedResult;
      }
    }
    return null;
  }

  private processQuestionMatch(question: any): AnswerSearchResult {
    const selectedAnswer = this.findSelectedAnswer(question.answers);
    if (selectedAnswer) {
      return selectedAnswer;
    }

    if (question.numericValue !== undefined) {
      return { numericValue: question.numericValue };
    }

    return {};
  }

  private findSelectedAnswer(answers: any[]): AnswerSearchResult | null {
    if (!answers) {
      return null;
    }

    for (const answer of answers) {
      if (answer.selected) {
        return {
          selectedAnswerId: answer.id,
          hasSelectedAnswer: true,
          textValue: answer.textValue,
          numericValue: answer.numericValue,
        };
      }
    }

    return null;
  }

  private searchNestedQuestions(questionCode: string, question: any): AnswerSearchResult | null {
    // Check nested questions
    if (question.questions) {
      const result = this.findAnswerInQuestions(questionCode, question.questions);
      if (result) {
        return result;
      }
    }

    // Check questions in answers (follow-up questions)
    if (question.answers) {
      for (const answer of question.answers) {
        if (answer.questions) {
          const result = this.findAnswerInQuestions(questionCode, answer.questions);
          if (result) {
            return result;
          }
        }
      }
    }

    return null;
  }

  private validateNumericAnswer(value: number, question: any): void {
    // Check for explicit range properties
    const answerWithRange = question.answers?.find((a) => a.range);
    if (answerWithRange?.range) {
      this.validateExplicitRange(value, answerWithRange.range);
      return;
    }

    // Apply specific validation rules for known questions
    this.validateSpecificQuestionRules(value, question.code);

    // General validation
    this.validateGeneralNumericRules(value);
  }

  private validateExplicitRange(value: number, range: any): void {
    const { min, max } = range;
    if (value < min || value > max) {
      throw new BadRequestException(`Value must be between ${min} and ${max}`);
    }
  }

  private validateSpecificQuestionRules(value: number, questionCode: string): void {
    if (questionCode === 'HBI_6' || questionCode === 'SCCAI_8') {
      // Fecal Calprotectin validation: reasonable medical range
      if (value < 0) {
        throw new BadRequestException('Value cannot be negative');
      }
      if (value > **********) {
        throw new BadRequestException('Value cannot exceed **********');
      }
    }
  }

  private validateGeneralNumericRules(value: number): void {
    if (value < 0) {
      throw new BadRequestException('Value cannot be negative');
    }
  }

  private getDemoTestResult(id: string, user, updateData) {
    const now = new Date();
    const { score, testType } = this.calculateDemoScore(id);

    return {
      id,
      status: TestResultStatus.COMPLETED,
      totalScores: score,
      completedDate: now.getTime() / 1000,
      message: 'Demo test completed! Results are not saved.',
      isDemoResult: true,
    };
  }

  private calculateDemoScore(id: string): { score: number; testType: TestType } {
    const demoScores = {
      'demo-hbi-test': {
        testType: TestType.SYMPTOMS,
        score: crypto.randomInt(3, 19), // HBI range: 0-16, demo range: 3-18
      },
      'demo-sccai-test': {
        testType: TestType.SYMPTOMS,
        score: crypto.randomInt(2, 13), // SCCAI range: 0-12, demo range: 2-12
      },
      'demo-mental-wellbeing-test': {
        testType: TestType.MENTAL_WELLBEING,
        score: crypto.randomInt(5, 26), // Demo range: 5-25 seconds
      },
      'demo-general-wellbeing-test': {
        testType: TestType.GENERAL_WELLBEING,
        score: crypto.randomInt(40, 91), // Demo range: 40-90
      },
    };

    return demoScores[id] || { score: 0, testType: TestType.SYMPTOMS };
  }

  /**
   * Get test type name for logging purposes
   */
  private getTestTypeName(testType: TestType): string {
    const testTypeNames = {
      [TestType.SYMPTOMS]: 'Symptoms',
      [TestType.MENTAL_WELLBEING]: 'Mental Wellbeing',
      [TestType.GENERAL_WELLBEING]: 'General Wellbeing',
    };

    return testTypeNames[testType] || 'Unknown';
  }

  /**
   * Get chart data for all test types for a specific patient
   * Implements functional requirement 6.4.4.2: "The Clinician will be able to view graphic results for an individual patient"
   */
  public async getPatientChartData(patientId: string, query: ChartQueryDto): Promise<PatientChartDataRO> {
    // Validate patient exists and is not archived
    const patient = await this.validatePatientExists(patientId);

    // Get chart data for all test types
    const chartData = await this.getChartDataForAllTestTypes(patientId, query);

    // Build patient header info
    const patientHeader: PatientHeaderInfo = {
      id: patient._id.toString(),
      fullName: patient.fullName || `${patient.firstName} ${patient.lastName}`,
      nhsNumber: patient.nhsNumber,
    };

    // Build table data for all test types
    const tables = await this.buildAllTableData(patientId, query);

    return {
      patient: patientHeader,
      symptomsChart: chartData.symptoms,
      mentalWellbeingChart: chartData.mentalWellbeing,
      generalWellbeingChart: chartData.generalWellbeing,
      tables,
    };
  }

  /**
   * Get chart data for a specific test type for a patient
   * Implements functional requirement 6.4.4.2 for individual test type visualization
   */
  public async getPatientChartDataByType(
    patientId: string,
    testType: TestType,
    query: SingleTestChartQueryDto,
  ): Promise<SingleTestChartDataRO> {
    // Validate patient exists and is not archived
    const patient = await this.validatePatientExists(patientId);

    // Build patient header info
    const patientHeader: PatientHeaderInfo = {
      id: patient._id.toString(),
      fullName: patient.fullName || `${patient.firstName} ${patient.lastName}`,
      nhsNumber: patient.nhsNumber,
    };

    let chartData: SymptomsChartData | MentalWellbeingChartData | GeneralWellbeingChartData;

    // Get chart data based on test type
    switch (testType) {
      case TestType.SYMPTOMS:
        chartData = await this.buildSymptomsChartData(patientId, query);
        break;
      case TestType.MENTAL_WELLBEING:
        chartData = await this.buildMentalWellbeingChartData(patientId, query);
        break;
      case TestType.GENERAL_WELLBEING:
        chartData = await this.buildGeneralWellbeingChartData(patientId, query);
        break;
      default:
        throw new BadRequestException('Invalid test type');
    }

    // Build table data for the specific test type
    const tableData = await this.buildTableDataForTestType(patientId, testType, query);

    return {
      patient: patientHeader,
      chartData,
      tableData,
    };
  }

  /**
   * Validate that patient exists and is not archived
   */
  private async validatePatientExists(patientId: string): Promise<any> {
    const patient = await this.userModel.findOne({
      _id: new Types.ObjectId(patientId),
      isArchived: { $ne: true },
    });

    if (!patient) {
      throw new NotFoundException('Patient not found');
    }

    return patient;
  }

  /**
   * Get chart data for all test types
   */
  private async getChartDataForAllTestTypes(
    patientId: string,
    query: ChartQueryDto,
  ): Promise<{
    symptoms?: SymptomsChartData;
    mentalWellbeing?: MentalWellbeingChartData;
    generalWellbeing?: GeneralWellbeingChartData;
  }> {
    const result: any = {};

    // Get symptoms chart data
    try {
      result.symptoms = await this.buildSymptomsChartData(patientId, query);
    } catch (error) {
      this.logger.warn(`No symptoms data available for patient ${patientId}`);
    }

    // Get mental wellbeing chart data
    try {
      result.mentalWellbeing = await this.buildMentalWellbeingChartData(patientId, query);
    } catch (error) {
      this.logger.warn(`No mental wellbeing data available for patient ${patientId}`);
    }

    // Get general wellbeing chart data
    try {
      result.generalWellbeing = await this.buildGeneralWellbeingChartData(patientId, query);
    } catch (error) {
      this.logger.warn(`No general wellbeing data available for patient ${patientId}`);
    }

    return result;
  }

  /**
   * Build table data for all test types
   */
  private async buildAllTableData(
    patientId: string,
    query: ChartQueryDto,
  ): Promise<{
    symptoms?: TestResultTableData;
    mentalWellbeing?: TestResultTableData;
    generalWellbeing?: TestResultTableData;
  }> {
    const result: any = {};

    // Build symptoms table data
    try {
      result.symptoms = await this.buildTableDataForTestType(patientId, TestType.SYMPTOMS, query);
    } catch (error) {
      this.logger.warn(`No symptoms table data available for patient ${patientId}`);
    }

    // Build mental wellbeing table data
    try {
      result.mentalWellbeing = await this.buildTableDataForTestType(patientId, TestType.MENTAL_WELLBEING, query);
    } catch (error) {
      this.logger.warn(`No mental wellbeing table data available for patient ${patientId}`);
    }

    // Build general wellbeing table data
    try {
      result.generalWellbeing = await this.buildTableDataForTestType(patientId, TestType.GENERAL_WELLBEING, query);
    } catch (error) {
      this.logger.warn(`No general wellbeing table data available for patient ${patientId}`);
    }

    return result;
  }

  /**
   * Build symptoms chart data for HBI/SCCAI tests
   * Requirements: "RAG results will be displayed as a bar graph, y-axis = score, x-axis = date"
   */
  private async buildSymptomsChartData(patientId: string, query: ChartQueryDto): Promise<SymptomsChartData> {
    const testResults = await this.getTestResultsForChart(patientId, TestType.SYMPTOMS, query);

    if (!testResults || testResults.length === 0) {
      throw new NotFoundException('There is no available data for this test. Please refresh or try again later.');
    }

    // Determine IBD subtype from first result to set chart title
    const ibdSubtype = testResults[0]?.patient?.ibdSubtype;
    const title = ibdSubtype === IBDSubtype.CROHNS_DISEASE ? 'HBI Survey' : 'SCCAI Survey';

    const data = testResults.map((result) => ({
      date: result.completedDate.toISOString(),
      score: result.totalScores || 0,
      ragFlags: {
        red: result.reds || 0,
        amber: result.ambers || 0,
        green: result.greens || 0,
      },
    }));

    return { title, data };
  }

  /**
   * Build mental wellbeing chart data for A&D tests
   * Requirements: "A & D results will be displayed as a line graph with separate lines"
   */
  private async buildMentalWellbeingChartData(
    patientId: string,
    query: ChartQueryDto,
  ): Promise<MentalWellbeingChartData> {
    const testResults = await this.getTestResultsForChart(patientId, TestType.MENTAL_WELLBEING, query);

    if (!testResults || testResults.length === 0) {
      throw new NotFoundException('There is no available data for this test. Please refresh or try again later.');
    }

    const anxietyData = testResults
      .filter((result) => result.anxietyScore !== undefined && result.anxietyScore !== null)
      .map((result) => ({
        date: result.completedDate.toISOString(),
        score: result.anxietyScore,
      }));

    const depressionData = testResults
      .filter((result) => result.depressionScore !== undefined && result.depressionScore !== null)
      .map((result) => ({
        date: result.completedDate.toISOString(),
        score: result.depressionScore,
      }));

    return { anxietyData, depressionData };
  }

  /**
   * Build general wellbeing chart data for SIBDQ tests
   * Requirements: "SIBDQ scores will be displayed as a line graph"
   */
  private async buildGeneralWellbeingChartData(
    patientId: string,
    query: ChartQueryDto,
  ): Promise<GeneralWellbeingChartData> {
    const testResults = await this.getTestResultsForChart(patientId, TestType.GENERAL_WELLBEING, query);

    if (!testResults || testResults.length === 0) {
      throw new NotFoundException('There is no available data for this test. Please refresh or try again later.');
    }

    const sibdqData = testResults
      .filter((result) => result.sibdqScore !== undefined && result.sibdqScore !== null)
      .map((result) => ({
        date: result.completedDate.toISOString(),
        score: result.sibdqScore,
      }));

    return { sibdqData };
  }

  /**
   * Build table data for a specific test type
   * Requirements: "Each table will be defaulted to 10 items per page, Apply pagination"
   */
  private async buildTableDataForTestType(
    patientId: string,
    testType: TestType,
    query: ChartQueryDto | SingleTestChartQueryDto,
  ): Promise<TestResultTableData> {
    const page = query.page || 1;
    const limit = query.limit || 10;
    const skip = (page - 1) * limit;

    // Get paginated test results
    const testResults = await this.getTestResultsForTable(patientId, testType, query, skip, limit);
    const totalDocs = await this.getTestResultsCount(patientId, testType, query);

    const items: TestResultTableRow[] = testResults.map((result) => {
      const baseRow: TestResultTableRow = {
        id: result._id.toString(),
        dateTime: result.completedDate.toISOString(), // UTC timestamp as per user preferences
        incomplete: result.status === TestResultStatus.NEW,
        flagged: result.flagDetails && result.flagDetails.length > 0,
      };

      // Add test type specific fields
      switch (testType) {
        case TestType.SYMPTOMS:
          baseRow.score = result.totalScores;
          baseRow.ragFlags = {
            red: result.reds || 0,
            amber: result.ambers || 0,
            green: result.greens || 0,
          };
          break;
        case TestType.MENTAL_WELLBEING:
          baseRow.anxietyScore = result.anxietyScore;
          baseRow.depressionScore = result.depressionScore;
          break;
        case TestType.GENERAL_WELLBEING:
          baseRow.sibdqScore = result.sibdqScore;
          baseRow.eqScore = result.eqScore;
          break;
      }

      return baseRow;
    });

    const totalPages = Math.ceil(totalDocs / limit);

    return {
      items,
      totalDocs,
      limit,
      totalPages,
      page,
    };
  }

  /**
   * Get test results for chart data (optimized for chart rendering)
   */
  private async getTestResultsForChart(
    patientId: string,
    testType: TestType,
    query: ChartQueryDto,
  ): Promise<RawTestResultForChart[]> {
    const matchConditions: any = {
      patient: new Types.ObjectId(patientId),
      type: testType,
      status: { $ne: TestResultStatus.NEW },
    };

    // Add date filtering if provided
    if (query.startDate || query.endDate) {
      matchConditions.completedDate = {};
      if (query.startDate) {
        matchConditions.completedDate.$gte = new Date(query.startDate);
      }
      if (query.endDate) {
        matchConditions.completedDate.$lte = new Date(query.endDate);
      }
    }

    const aggregationPipeline = [
      { $match: matchConditions },
      {
        $lookup: {
          from: 'users',
          localField: 'patient',
          foreignField: '_id',
          as: 'patient',
        },
      },
      { $unwind: '$patient' },
      {
        $match: {
          'patient.isArchived': { $ne: true },
        },
      },
      {
        $project: {
          completedDate: 1,
          totalScores: 1,
          reds: 1,
          ambers: 1,
          greens: 1,
          anxietyScore: 1,
          depressionScore: 1,
          sibdqScore: 1,
          eqScore: 1,
          status: 1,
          flagDetails: 1,
          'patient._id': 1,
          'patient.firstName': 1,
          'patient.lastName': 1,
          'patient.fullName': 1,
          'patient.nhsNumber': 1,
          'patient.ibdSubtype': 1,
        },
      },
      { $sort: { completedDate: 1 } }, // Sort by date ascending for chart display
    ];

    return await this.testResultModel.aggregate(aggregationPipeline as any);
  }

  /**
   * Get test results for table data (with pagination)
   */
  private async getTestResultsForTable(
    patientId: string,
    testType: TestType,
    query: ChartQueryDto | SingleTestChartQueryDto,
    skip: number,
    limit: number,
  ): Promise<RawTestResultForChart[]> {
    const matchConditions: any = {
      patient: new Types.ObjectId(patientId),
      type: testType,
      status: { $ne: TestResultStatus.NEW },
    };

    // Add date filtering if provided
    if (query.startDate || query.endDate) {
      matchConditions.completedDate = {};
      if (query.startDate) {
        matchConditions.completedDate.$gte = new Date(query.startDate);
      }
      if (query.endDate) {
        matchConditions.completedDate.$lte = new Date(query.endDate);
      }
    }

    // Determine sort order
    const sortField = query.sortBy || 'completedDate';
    const sortOrder = query.sortOrder === 'asc' ? 1 : -1;
    const sortCondition = { [sortField]: sortOrder };

    const aggregationPipeline = [
      { $match: matchConditions },
      {
        $lookup: {
          from: 'users',
          localField: 'patient',
          foreignField: '_id',
          as: 'patient',
        },
      },
      { $unwind: '$patient' },
      {
        $match: {
          'patient.isArchived': { $ne: true },
        },
      },
      {
        $project: {
          completedDate: 1,
          totalScores: 1,
          reds: 1,
          ambers: 1,
          greens: 1,
          anxietyScore: 1,
          depressionScore: 1,
          sibdqScore: 1,
          eqScore: 1,
          status: 1,
          flagDetails: 1,
          'patient._id': 1,
          'patient.firstName': 1,
          'patient.lastName': 1,
          'patient.fullName': 1,
          'patient.nhsNumber': 1,
          'patient.ibdSubtype': 1,
        },
      },
      { $sort: sortCondition },
      { $skip: skip },
      { $limit: limit },
    ];

    return await this.testResultModel.aggregate(aggregationPipeline as any);
  }

  /**
   * Get count of test results for pagination
   */
  private async getTestResultsCount(
    patientId: string,
    testType: TestType,
    query: ChartQueryDto | SingleTestChartQueryDto,
  ): Promise<number> {
    const matchConditions: any = {
      patient: new Types.ObjectId(patientId),
      type: testType,
      status: { $ne: TestResultStatus.NEW },
    };

    // Add date filtering if provided
    if (query.startDate || query.endDate) {
      matchConditions.completedDate = {};
      if (query.startDate) {
        matchConditions.completedDate.$gte = new Date(query.startDate);
      }
      if (query.endDate) {
        matchConditions.completedDate.$lte = new Date(query.endDate);
      }
    }

    const aggregationPipeline = [
      { $match: matchConditions },
      {
        $lookup: {
          from: 'users',
          localField: 'patient',
          foreignField: '_id',
          as: 'patient',
        },
      },
      { $unwind: '$patient' },
      {
        $match: {
          'patient.isArchived': { $ne: true },
        },
      },
      { $count: 'total' },
    ];

    const result = await this.testResultModel.aggregate(aggregationPipeline as any);
    return result.length > 0 ? result[0].total : 0;
  }
}
